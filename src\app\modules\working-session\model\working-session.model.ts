import { Column, SeHttpResponse } from 'se-ui-components-mf-lib';

export interface WorkingSession {
  idTramit: string;
  nifSubPas: string;
  nomSubPas: string;
  dataSession: Date;
  dadesAditional?: string;
  exercici?: string;
}

export enum WorkingSessionKeys {
  SessionDate = 'dataSession',
  Declarant = 'nomSubPas',
  Year = 'exercici',
  Establishment = 'dadesAditional',
  Actions = 'actions',
}

export interface WorkingSessionResponse extends SeHttpResponse {
  content?: {
    sessionsTreball: WorkingSession[];
  };
}

export const WORKING_SESSION_HEADERS: Column[] = [
  {
    header:
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_WORKING_SESSION.W_S.TABLE_HEADERS.SESSION_DATE',
    key: WorkingSessionKeys.SessionDate,
    cellComponentName: 'dateCellComponent',
    resizable: false,
    sortable: true,
    size: 13,
    cellConfig: {
      dateFormat: 'dd/MM/yyyy hh:mm',
    },
  },
  {
    header:
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_WORKING_SESSION.W_S.TABLE_HEADERS.DECLARANT',
    key: WorkingSessionKeys.Declarant,
    resizable: true,
    sortable: true,
  },
  {
    header:
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_WORKING_SESSION.W_S.TABLE_HEADERS.YEAR',
    key: WorkingSessionKeys.Year,
    resizable: true,
    sortable: true,
    size: 14,
  },
  {
    header:
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_WORKING_SESSION.W_S.TABLE_HEADERS.ESTABLISHMENT',
    key: WorkingSessionKeys.Establishment,
    resizable: true,
    sortable: true,
  },
  {
    header:
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_WORKING_SESSION.W_S.TABLE_HEADERS.ACTIONS',
    key: WorkingSessionKeys.Actions,
    size: 8,
    resizable: false,
    cellComponentName: 'editCellComponent',
    cellConfig: {},
  },
];
