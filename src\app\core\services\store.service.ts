import { Injectable } from '@angular/core';

import {
  Nullable,
  SeDataStorageService,
  type SeHeaderInfoItem,
  type TagProps,
} from 'se-ui-components-mf-lib';
import { TaxPayer } from 'src/app/modules/participants/models';

/* Storage keys */
const ID_TRAMIT = 'IGEC_ID_TRAMIT';
const SELECTED_TAXPAYER = 'IGEC_SELECTED_TAXPAYER';
const IS_COUNTING_MODE_WITH_INSTRUMENT =
  'IGEC_IS_COUNTING_MODE_WITH_INSTRUMENT';
const ESTABLISHMENT_CATEGORY_ID = 'IGEC_ESTABLISHMENT_CATEGORY_ID';
const TAXYEAR = 'IGEC_TAXYEAR';
const DECLARED = 'IGEC_DECLARED';
const AMOUNT_TO_PAY = 'IGEC_AMOUNT_TO_PAY';
const SELFASSESSMENT_ID = 'IGEC_SELFASSESSMENT_ID';
const MODEL = 'IGEC_MODEL';
const PRESENTED_AL = 'IGEC_PRESENTED_AL';
const PRESENTATION_DATE = 'IGEC_PRESENTATION_DATE';
const PAYMENT_ID = 'IGEC_PAYMENT_ID';
const HEADER = 'IGEC_HEADER';
const STATUS = 'IGEC_STATUS';
const ESTABLISHMENT = 'IGEC_ESTABLISHMENT';
const WORKING_SESSION_HAS_APPEARED = 'IGEC_WORKING_SESSION_HAS_APPEARED';
const RESPONSIBLE_DECLARATION_CHECKED = 'IGEC_RESPONSIBLE_DECLARATION_CHECKED';

@Injectable({
  providedIn: 'root',
})
export class StoreService {
  constructor(private storage: SeDataStorageService) {
    // Intencionadamente vacío
  }

  get idTramit(): Nullable<string> {
    return this.storage.getItem(ID_TRAMIT);
  }
  set idTramit(idTramit: Nullable<string>) {
    this.storage.setItem(ID_TRAMIT, idTramit);
  }

  get hasAppearedWorkingSession(): Nullable<boolean> {
    return this.storage.getItem(WORKING_SESSION_HAS_APPEARED);
  }
  set hasAppearedWorkingSession(hasAppeared: Nullable<boolean>) {
    this.storage.setItem(WORKING_SESSION_HAS_APPEARED, hasAppeared);
  }

  get selectedTaxpayer(): Nullable<TaxPayer> {
    return this.storage.getItem(SELECTED_TAXPAYER);
  }
  set selectedTaxpayer(taxpayer: Nullable<TaxPayer>) {
    this.storage.setItem(SELECTED_TAXPAYER, taxpayer);
  }

  get isCountingModeWithInstrument(): Nullable<boolean> {
    return this.storage.getItem(IS_COUNTING_MODE_WITH_INSTRUMENT);
  }
  set isCountingModeWithInstrument(
    isCountingModeWithInstrument: Nullable<boolean>,
  ) {
    this.storage.setItem(
      IS_COUNTING_MODE_WITH_INSTRUMENT,
      isCountingModeWithInstrument,
    );
  }

  get establishmentCategoryId(): Nullable<string> {
    return this.storage.getItem(ESTABLISHMENT_CATEGORY_ID);
  }
  set establishmentCategoryId(establishmentCategoryId: Nullable<string>) {
    this.storage.setItem(ESTABLISHMENT_CATEGORY_ID, establishmentCategoryId);
  }

  get declared(): Nullable<boolean> {
    return this.storage.getItem(DECLARED);
  }
  set declared(declared: Nullable<boolean>) {
    this.storage.setItem(DECLARED, declared);
  }
  get taxYear(): Nullable<number> {
    return this.storage.getItem(TAXYEAR);
  }
  set taxYear(taxYear: Nullable<number>) {
    this.storage.setItem(TAXYEAR, taxYear);
  }

  get amountToPay(): Nullable<number> {
    return this.storage.getItem(AMOUNT_TO_PAY);
  }
  set amountToPay(amount: Nullable<number>) {
    this.storage.setItem(AMOUNT_TO_PAY, amount);
  }

  get selfassessmentId(): Nullable<string> {
    return this.storage.getItem(SELFASSESSMENT_ID);
  }
  set selfassessmentId(selfassessmentId: Nullable<string>) {
    this.storage.setItem(SELFASSESSMENT_ID, selfassessmentId);
  }

  get model(): Nullable<string> {
    return this.storage.getItem(MODEL);
  }
  set model(model: Nullable<string>) {
    this.storage.setItem(MODEL, model);
  }

  get presentedAl(): Nullable<boolean> {
    return this.storage.getItem(PRESENTED_AL);
  }
  set presentedAl(presented: Nullable<boolean>) {
    this.storage.setItem(PRESENTED_AL, presented);
  }

  get responsibleDeclaration(): Nullable<boolean> {
    return this.storage.getItem(RESPONSIBLE_DECLARATION_CHECKED);
  }
  set responsibleDeclaration(check: Nullable<boolean>) {
    this.storage.setItem(RESPONSIBLE_DECLARATION_CHECKED, check);
  }

  get paymentId(): Nullable<string> {
    return this.storage.getItem(PAYMENT_ID);
  }
  set paymentId(id: Nullable<string>) {
    this.storage.setItem(PAYMENT_ID, id);
  }

  get header(): Nullable<SeHeaderInfoItem>[] {
    return this.storage.getItem(HEADER);
  }
  set header(header: Nullable<SeHeaderInfoItem>[]) {
    this.storage.setItem(HEADER, header);
  }
  get status(): Nullable<TagProps[]> {
    return this.storage.getItem(STATUS);
  }
  set status(header: Nullable<TagProps[]>) {
    this.storage.setItem(STATUS, header);
  }
  get establishment(): Nullable<string> {
    return this.storage.getItem(ESTABLISHMENT);
  }
  set establishment(establishment: Nullable<string>) {
    this.storage.setItem(ESTABLISHMENT, establishment);
  }
  get presentationDate(): Nullable<string> {
    return this.storage.getItem(PRESENTATION_DATE);
  }
  set presentationDate(presentationDate: Nullable<string>) {
    this.storage.setItem(PRESENTATION_DATE, presentationDate);
  }
}
