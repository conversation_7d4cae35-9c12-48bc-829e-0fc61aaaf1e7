import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { StoreService } from '../services';
import { AppRoutes } from '../models';

@Injectable({ providedIn: 'root' })
export class YearEstablishmentGuard {
  constructor(private router: Router, private store: StoreService) {}

  canActivate() {
    if (this.store.idTramit) {
      return true;
    } else {
      this.router.navigate([AppRoutes.PARTICIPANTS]);
      return false;
    }
  }
}
