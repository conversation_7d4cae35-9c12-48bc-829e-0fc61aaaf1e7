<ng-container *ngIf="selfassessmentId">
  <se-alert
    *ngIf="alertMessage"
    [title]="alertMessage.title"
    [list]="alertMessage.list"
    [type]="alertMessage.type"
    [closeButton]="true"
  >
    <span *ngIf="alertDescription">{{ alertDescription | translate }}</span>
  </se-alert>

  <mf-tributs-self-assessment-result
    *axLazyElement
    [selfassessments]="[selfassessmentId]"
    [showDownloadMenu]="false"
    [actions]="actions"
    [columns]="headers"
    (payButtonEvent)="goToPagament()"
  >
  </mf-tributs-self-assessment-result>
</ng-container>
