import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { SeAlertModule, SeButtonModule, SpinnerComponent } from 'se-ui-components-mf-lib';
import { ResultSelfAssessmentComponent } from './result-selfassessment.component';
import { environment } from 'src/environments/environment';

const routes: Routes = [
  {
    path: '',
    component: ResultSelfAssessmentComponent,
    data: {
      title: 'SE_ANTENES_MF.APP_TITLE',
      isElementVisible: false
    },
  },
];

@NgModule({
  declarations: [ResultSelfAssessmentComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-tributs-self-assessment-result',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
      ]
    }),
    SeButtonModule,
    SeAlertModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ResultSelfAssessmentModule {}
