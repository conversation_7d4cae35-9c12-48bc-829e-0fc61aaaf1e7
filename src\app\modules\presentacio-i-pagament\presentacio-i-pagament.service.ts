import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { Subject, take, takeUntil } from 'rxjs';

import { SeModalService, SeProgressModal } from 'se-ui-components-mf-lib';
import {
  AppRoutes,
  HeaderInfoService,
  StoreService,
  type SelfAssessmentStatus,
} from 'src/app/core';
import { StatusAutoliquidacio } from './models/status-autoliquidacio.model';
import { PresentacioIPagamentsEndpointsService } from './presentacio-i-pagament-endpoints.service';

@Injectable({
  providedIn: 'root'
})
export class PresentacioIPagamentsService {

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private storeService: StoreService,
    private endpointsService: PresentacioIPagamentsEndpointsService,
    private router: Router,
    private modalService: SeModalService,
    private translateService: TranslateService,
    private header: HeaderInfoService
  ) { }
  
  runPresentacio = (modalRef: NgbModalRef, navigate = true): void => {
    let idAutoliquidacio: string;
    
    try {
      idAutoliquidacio = this.storeService.selfassessmentId!;
    } catch(e) {
      modalRef.close();
      //go to index or show error
      return;
    }

    this.endpointsService.postPresentation(idAutoliquidacio).pipe(
      take(1)
    )
    .subscribe({
      next: () => {
        this.checkStatusAutoliquidacio(modalRef, idAutoliquidacio, navigate);
      },
      error: () => {
        //SHOW ERROR?
        modalRef.close();
      }
    });
  }

  runPagament = (): void => {
    let idAutoliquidacio: string;
    
    try {
      idAutoliquidacio = this.storeService.selfassessmentId!;
    } catch(e) {
      //go to index or show error
      return;
    }

    const progressModal: SeProgressModal = {
      interval: 10,
      message: this.translateService.instant('SE_GRANS_ESTABLIMENTS_MF.MODULE_PRESENTACION_I_PAGAMENT.PAGAMENT.MODAL_MESSAGE')
    }

    const modalRef = this.modalService.openProgressModal(progressModal.interval!, progressModal.message!);

    this.checkStatusAutoliquidacio(modalRef, idAutoliquidacio, true);
  }

  private checkStatusAutoliquidacio = (modalRef: NgbModalRef, idAutoliquidacio: string, navigate = true): void => {
    //CHANGE MESSAGE
    modalRef.componentInstance.intervalOutput
    .pipe(
      takeUntil(this.unsubscribe)
    )
    .subscribe(() => {
      this.endpointsService.getStatusAutoliquidacio(idAutoliquidacio)
      .pipe(
        takeUntil(this.unsubscribe)
      )
      .subscribe({
        next: (response) => {
          this.header.status = response?.content.estat as SelfAssessmentStatus;
          this.header.presentationDate = response?.content.dataPresentacio;
          if(this.isPresentat(response?.content.estat) || this.isPagat(response?.content.estat) || this.hasError(response?.content.estat)) {
            this.closeAndDestroy(modalRef, response?.content.estat);
            this.storeService.presentedAl = true;
            if (navigate || this.hasError(response?.content.estat)) {
              console.log('Attempting navigation to RESULT with:', {
                idAutoliquidacio: response?.content.idAutoliquidacio,
                estat: response?.content.estat,
                route: [AppRoutes.RESULT, response?.content.idAutoliquidacio]
              });
              this.router.navigate([AppRoutes.RESULT, response?.content.idAutoliquidacio], { state:{ statusAutoliquidacio: response?.content.estat }});
            }
          }
        },
        error: () => {
          //SHOW ERROR?
          this.closeAndDestroy(modalRef);
        }
      });
    });
  }

  private isPresentat = (estat: StatusAutoliquidacio | undefined): boolean => estat === StatusAutoliquidacio.PRESENTAT || estat === StatusAutoliquidacio.PENDENT_PAGAMENT;
  private isPagat = (estat: StatusAutoliquidacio | undefined): boolean => estat === StatusAutoliquidacio.PAGAT;
  private hasError = (estat: StatusAutoliquidacio | undefined): boolean => estat === StatusAutoliquidacio.PRESENTACIO_ERROR || estat === StatusAutoliquidacio.PAGAMENT_ERROR || estat === StatusAutoliquidacio.ERROR;

  private closeAndDestroy = (modalRef: NgbModalRef, estat: StatusAutoliquidacio | undefined = undefined): void => {
     //If has state and is not error
     modalRef.close(estat && !this.hasError(estat));
    this.onDestroy();
  }

  private onDestroy = (): void => {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

}