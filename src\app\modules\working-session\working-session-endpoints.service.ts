import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { Observable } from 'rxjs';

import { environment } from 'src/environments/environment';
import { ParticipantsResponse } from './model/participants.model';
import { WorkingSessionResponse } from './model/working-session.model';

@Injectable({
  providedIn: 'root',
})
export class WorkingSessionRequestService {
  constructor(private httpService: SeHttpService) {}

  getWorkingSessions(tax: string): Observable<WorkingSessionResponse> {
    const params = new HttpParams().set('impost', tax);

    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/sessions-treball`,
      method: 'get',
      clearExceptions: true,
      params,
    };

    return this.httpService.get(httpRequest);
  };

  getParticipantData(procedureId: string): Observable<ParticipantsResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlTributs,
      url: `/intervinents/${procedureId}`,
      method: 'get',
    });
  }
}