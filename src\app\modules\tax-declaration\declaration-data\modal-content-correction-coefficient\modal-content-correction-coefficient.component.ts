import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Column, Row, SeModal } from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-modal-content-correction-coefficient',
  styleUrls: ['modal-content-correction-coefficient.component.scss'],
  templateUrl: './modal-content-correction-coefficient.component.html',
})
export class ModalContentCorrectionCoefficientComponent {
  tableBodyData: Row[] = [];
  @Input() data!: SeModal;
  @Input() typologyPlacesForCoefficientCorrectorModal: string[] = [];
  @Input() set tableBodyCoefficientCorrector(body: Row[]) {
    if (body) {
      this.tableBodyData = body;
    }
  }

  headerColumns: Column[] = [];

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService
  ) {
    this.headerColumns = [
      {
        header: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.PERCENTAGE_PLACES'
        ),
        key: 'descripcio',
        resizable: true,
      },
      {
        header: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.CORRECTION_COEFFICIENT'
        ),
        key: 'valor',
        resizable: true,
        cellConfig: {
          align: 'right',
        },
      },
    ];
  }

  closeModal() {
    this.activatedModalService.close();
  }
}
