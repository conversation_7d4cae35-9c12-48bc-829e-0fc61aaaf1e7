import { SeHttpResponse } from "se-ui-components-mf-lib";
import { Autoliquidacio } from "./autoliquidacio.model";

export enum StatusAutoliquidacio {
  GENERAT = 'GENERAT',
  ESBORRANY = 'ESBORRANY',
  NO_PRESENTAT = 'NO_PRESENTAT',
  PENDENT_PRESENTACIO = 'PENDENT_PRESENTACIO',
  PRESENTANT = 'PRESENTANT',
  PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
  PAGANT = 'PAGANT',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PAGAMENT_ERROR = 'PAGAMENT_ERROR',
  TRAMITAT = 'TRAMITAT',
  TRAMITANT = 'TRAMITANT',
  TRAMITACIO_ERROR = 'TRAMITACIO_ERROR',
  CONSOLIDANT = 'CONSOLIDANT',
  CONSOLIDAT = 'CONSOLIDAT',
  CONSOLIDACIO_ERROR = 'CONSOLIDACIO_ERROR',
  ERROR = 'ERROR',
}

export interface StatusAutoliquidacioResponse extends SeHttpResponse {
  content: Autoliquidacio;
}
