import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { StoreService } from '../services';
import { ID_AUTOLIQUIDACIO_PARAM } from '../models';

@Injectable({ providedIn: 'root' })
export class ResultSelfAssessmentGuard {
  constructor(private router: Router, private store: StoreService) {}

  canActivate(route: ActivatedRouteSnapshot) {
    const paramValue = route.paramMap.get(ID_AUTOLIQUIDACIO_PARAM);
    const storeValue = this.store.selfassessmentId;

    console.log('ResultSelfAssessmentGuard - Route param:', paramValue);
    console.log('ResultSelfAssessmentGuard - Store value:', storeValue);
    console.log('ResultSelfAssessmentGuard - All params:', route.paramMap.keys);

    if (storeValue || paramValue) {
      console.log('ResultSelfAssessmentGuard - Access granted');
      return true;
    }

    console.log('ResultSelfAssessmentGuard - Access denied');
    return false;
  }
}