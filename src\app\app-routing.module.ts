import { NgModule } from '@angular/core';
import { RouterModule, Routes, mapToCanActivate } from '@angular/router';

import { AppRoutes } from './core';
import { TaxDeclarationGuard } from './core/guards/tax-declaration.guard';
import { YearEstablishmentGuard } from './core/guards/year-establishment.guard';
import { CalculationGuard } from './core/guards/calculation.guard';
import { PresentationGuard } from './core/guards/presentation.guard';
import { ResultSelfAssessmentGuard } from './core/guards/result-selfassessment.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: AppRoutes.WORKING_SESSION,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.ROOT,
    redirectTo: AppRoutes.WORKING_SESSION,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.WORKING_SESSION,
    loadChildren: () =>
      import(`./modules/working-session/working-session.module`).then(
        (module) => module.WorkingSessionModule
      ),
  },
  {
    path: AppRoutes.PARTICIPANTS,
    loadChildren: () =>
      import(`./modules/participants/participants.module`).then(
        (module) => module.ParticipantsModule
      ),
  },
  {
    path: AppRoutes.YEAR_ESTABLISHMENT,
    loadChildren: () =>
      import(`./modules/year-establishment/year-establishment.module`).then(
        (module) => module.YearEstablishmentModule
      ),
      canActivate: mapToCanActivate([YearEstablishmentGuard]),
  },
  {
    path: AppRoutes.TAX_DECLARATION,
    loadChildren: () =>
      import(`./modules/tax-declaration/tax-declaration.module`).then(
        (module) => module.TaxDeclarationModule
      ),
      canActivate: mapToCanActivate([TaxDeclarationGuard]),
  },
  {
    path: AppRoutes.SUMMARY_CALCULATION,
    loadChildren: () =>
      import(`./modules/calculation/calculation.module`).then(
        (module) => module.CalculationModule
      ),
      canActivate: mapToCanActivate([CalculationGuard]),
  },
  {
    path: AppRoutes.PRESENTATION,
    loadChildren: () =>
      import(`./modules/presentacio-i-pagament/presentacio-i-pagament.module`).then(
        (module) => module.PresentacioIPagamentModule
      ),
      canActivate: mapToCanActivate([PresentationGuard]),
  },
  {
    path: AppRoutes.RESULT,
    loadChildren: () =>
      import(`./modules/result-selfassessment/result-selfassessment.module`).then(
        (module) => module.ResultSelfAssessmentModule
      ),
      canActivate: mapToCanActivate([ResultSelfAssessmentGuard]),
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
