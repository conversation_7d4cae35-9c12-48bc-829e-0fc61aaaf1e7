import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  takeUntil,
  throttleTime,
} from 'rxjs';
import { SeDropdownOption } from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';
import { DecimalPipe } from '@angular/common';

import { ParkingSpaceDeclaration } from '../models/tax-declaration.model';
import { TypologyResponse } from '../models/typology.model';
import { CoefficientResponse } from '../models/coefficient-corrector.model';

@Component({
  selector: 'app-parking-places',
  styleUrls: ['parking-places.component.scss'],
  templateUrl: './parking-places.component.html',
})
export class ParkingPlacesComponent implements OnInit, OnD<PERSON>roy {
  protected formParkingPlaces!: FormGroup;
  protected hasNonCompliance = false;
  protected isAlertVisible = false;
  protected messageAlert = '';
  private destroyed$: Subject<void> = new Subject<void>();

  @Input() coefficientList: CoefficientResponse[] = [];
  @Input() typologyList: TypologyResponse[] = [];
  @Input() typologyEstablishmentsForParkingPlaces: SeDropdownOption[] = [];
  @Input() set parkingSpaceDeclaration(
    parkingSpaceDeclaration: ParkingSpaceDeclaration
  ) {
    if (parkingSpaceDeclaration) {
      const { isAuthorized, numberPlaces, surface, typology } =
        parkingSpaceDeclaration;
      this.formParkingPlaces.patchValue({
        isAuthorized,
        numberPlaces,
        surface,
        typology,
      });
    }
  }

  @Output() handleSaveParkingPlaces: EventEmitter<ParkingSpaceDeclaration> =
    new EventEmitter();
  @Output() handleDisableDeclarationDataForm: EventEmitter<void> =
    new EventEmitter();

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private decimalPipe: DecimalPipe
  ) {}

  ngOnInit(): void {
    this.initParkingPlacesForm();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private initParkingPlacesForm(): void {
    this.formParkingPlaces = this.fb.group({
      numberPlaces: [
        null,
        [Validators.min(1), Validators.maxLength(7), Validators.max(9999999)],
      ],
      surface: [
        null,
        [Validators.min(1), Validators.max(999999.99), Validators.maxLength(9)],
      ],
      typology: null,
      isAuthorized: null,
    });

    this.formParkingPlaces.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        throttleTime(500),
        takeUntil(this.destroyed$)
      )
      .subscribe(({ typology, numberPlaces, surface, isAuthorized }) => {
        if (isAuthorized !== null) {
          if (isAuthorized) {
            this.formParkingPlaces.get('numberPlaces')?.disable();
            this.formParkingPlaces.get('surface')?.disable();
            this.formParkingPlaces.get('typology')?.disable();
          } else {
            this.formParkingPlaces.get('numberPlaces')?.enable();
            this.formParkingPlaces.get('surface')?.enable();
            this.formParkingPlaces.get('typology')?.enable();
          }

          this.setCoefficientCorrector(
            typology,
            numberPlaces,
            +surface,
            isAuthorized
          );
        } else if (!typology || !numberPlaces || !surface) {
          this.handleDisableDeclarationDataForm.emit();
          this.isAlertVisible = false;
          this.messageAlert = '';
        } else {
          this.setCoefficientCorrector(
            typology,
            numberPlaces,
            +surface,
            isAuthorized
          );
        }
      });
  }

  private setCoefficientCorrector(
    typology: number,
    numberPlaces: number,
    surface: number,
    isAuthorized: boolean
  ): void {
    if (typology && numberPlaces && surface) {
      const coefficientCorrectorPercent =
        (1 - (numberPlaces * 100) / surface / typology) * 100;

      const coefficient = this.getCoefficientCorrectorValue(
        coefficientCorrectorPercent
      );
      this.hasNonCompliance = coefficient > 1;

      const coefficientParsed = this.decimalPipe.transform(coefficient || 0, '1.0-3');
      this.setMessageAlert(coefficientCorrectorPercent, coefficientParsed!);

      const parkingPlaces = {
        coefficient,
        numberPlaces,
        surface,
        typology,
        isAuthorized,
      };
      this.handleSaveParkingPlaces.emit(parkingPlaces);
    } else {
      this.handleSaveParkingPlaces.emit({ isAuthorized });
    }
  }

  private setMessageAlert(
    coefficientCorrectorPercent: number,
    coefficient: string
  ): void {
    this.isAlertVisible = true;

    const coefficientPercentFixed = +coefficientCorrectorPercent.toFixed();

    // cuando es 0 o negativo, no es error
    // cuando es exacto el porcentaje a 0 o el redondeo del porcentaje es menor a 0
    if (coefficientPercentFixed < 0 || coefficientCorrectorPercent === 0 || coefficientCorrectorPercent < 0) {
      this.messageAlert = this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.ALERT_INFO_MESSAGE_NUMBER_SPACES_CORRECT',
        { coefficient }
      );
    } else if (coefficientPercentFixed === 0) {
      this.messageAlert = this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.ALERT_INFO_MESSAGE_NUMBER_SPACES_LESS_THAN_1',
        { coefficient }
      );
    } else {
      this.messageAlert = this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.ALERT_INFO_MESSAGE_NUMBER_SPACES_WRONG',
        {
          coefficient,
          percent: coefficientPercentFixed,
        }
      );
    }
  }

  private getCoefficientCorrectorValue(
    coefficientCorrectorPercent: number
  ): number {
    const coefficientSelected = this.coefficientList.find((coefficient) => {
      if (coefficient.intervalInicial < 0 && coefficientCorrectorPercent <= 0) {
        return coefficient.intervalFinal >= coefficientCorrectorPercent;
      } else {
        return (
          coefficient.intervalInicial < coefficientCorrectorPercent &&
          coefficient.intervalFinal >= coefficientCorrectorPercent
        );
      }
    });

    return coefficientSelected?.valor!;
  }
}
