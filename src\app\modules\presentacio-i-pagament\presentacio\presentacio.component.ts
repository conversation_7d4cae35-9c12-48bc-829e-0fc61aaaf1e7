import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { AppRoutes, StoreService } from 'src/app/core';
import { SeProgressModal } from 'se-ui-components-mf-lib';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PPRadioInput, PresentacioIPagamentModes } from './presentacio.model';
import { PresentacioIPagamentsService } from '../presentacio-i-pagament.service';

@Component({
  selector: 'app-presentacio',
  templateUrl: './presentacio.component.html',
  styleUrls: ['./presentacio.component.scss']
})
export class PresentacioComponent implements OnInit {

  AppRoutes = AppRoutes;
  progressModal!: SeProgressModal;
  pagament_data: PPRadioInput = {}

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private router: Router,
    private translateService: TranslateService,
    private presentacioService: PresentacioIPagamentsService,
    private location: Location,
    protected store: StoreService
  ) { }

  async ngOnInit() {
    this.progressModal = {
      interval: 10,
      message: await this.translateService.instant('SE_GRANS_ESTABLIMENTS_MF.MODULE_PRESENTACION_I_PAGAMENT.PRESENTACIO.MODAL_MESSAGE')
    }     
    
    this.pagament_data = { disabled : this.store.amountToPay! <= 0 }
  }

  ngOnDestroy() {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  onSelectedOption = (event: Event): void => {
    const customEvent = event as CustomEvent<PresentacioIPagamentModes>;
    const MODE_SELECTED = customEvent.detail;

    if(MODE_SELECTED === PresentacioIPagamentModes.PRESENTACIO_I_PAGAMENT) {
      this.store.presentedAl = true;
      this.router.navigate([AppRoutes.PAGAMENT], { state: { presentacioMode: true }});
    }
  }

  onOpenPresentationModal = (event: Event): void => {
    const customEvent = event as CustomEvent<NgbModalRef>;
    const modalRef = customEvent.detail;

    this.presentacioService.runPresentacio(modalRef);
  }

  goBack = () => { this.location.back() }
}
