<mf-tributs-working-sessions
  *axLazyElement
  [taxName]="tax"
  [selfAssessmentActions]="selfAssessmentActions"
  [workingSessionHeaders]="workingSessionHeaders"
  [workingSessionsTableData]="workingSessionsTableData"
  [workingSessionsPanelDescription]="
    'SE_GRANS_ESTABLIMENTS_MF.MODULE_WORKING_SESSION.W_S.DESCRIPTION'
      | translate
  "
  (handleSelfAssessmentActionButton)="onNavigateTogoPayment($event)"
  (handleSetSelfAssessment)="onSetSelfAssessment($event)"
  (handleSetSelfAssessmentSelected)="onSetSelfAssessmentSelected($event)"
  (handleEditWorkingSession)="onEditWorkingSession($event)"
  (handleUpdateWorkingSessionTable)="updateWorkingSessionTable()"
/>

<div class="mt-3 d-flex justify-content-end" *ngIf="workingSessions.length">
  <se-button (onClick)="submit()">{{
    'UI_COMPONENTS.BUTTONS.CONTINUE' | translate
  }}</se-button>
</div>
