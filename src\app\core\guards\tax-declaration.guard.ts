import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { StoreService } from '../services';
import { AppRoutes } from '../models';

@Injectable({ providedIn: 'root' })
export class TaxDeclarationGuard {
  constructor(private router: Router, private store: StoreService) {}

  canActivate() {
    if (
      this.store.isCountingModeWithInstrument !== undefined &&
      this.store.isCountingModeWithInstrument !== null
    ) {
      return true;
    } else {
      this.router.navigate([AppRoutes.YEAR_ESTABLISHMENT]);
      return false;
    }
  }
}
