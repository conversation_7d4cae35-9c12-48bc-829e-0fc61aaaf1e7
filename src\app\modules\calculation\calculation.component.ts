import { Component, OnInit, type OnD<PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { CurrencyPipe, DatePipe, DecimalPipe } from '@angular/common';

import { AppRoutes, HeaderInfoService, StoreService } from 'src/app/core';
import { CalculationEndpointService } from './calculation-endpoint.service';
import {
  CalculInfo,
  RequestPeriodInfo,
  RequestSaveSelfAssessment,
  ResponseCalcul,
  SummaryTemplate,
} from './calculation.model';
import { Constant } from 'src/app/core/models/constants.enum';

@Component({
  selector: 'app-calculation',
  styleUrls: [],
  templateUrl: 'calculation.component.html',
})
export class CalculationComponent implements OnInit, OnDestroy {
  private destroyed$: Subject<void>;

  /* CC SURCHARGE/INTEREST/SUMMARY */
  protected requestPeriodInfo: RequestPeriodInfo | undefined;
  protected calculationInfo: CalculInfo | undefined;
  protected template: SummaryTemplate[] | undefined;
  protected model: string = '';

  /* INFORMATION CALCULATION */
  private allCalculationInfo: ResponseCalcul | undefined;

  /* SAVE SELFASSESSMENT */
  private selfAssessmentInfo!: RequestSaveSelfAssessment;
  protected disabledButtonNext: boolean = true;

  protected alertTitle: string | undefined;
  private totalToPay: number = 0;

  constructor(
    private store: StoreService,
    private datePipe: DatePipe,
    private currencyPipe: CurrencyPipe,
    private decimalPipe: DecimalPipe,
    private calculationService: CalculationEndpointService,
    private translateService: TranslateService,
    private router: Router,
    private header: HeaderInfoService,
  ) {
    this.destroyed$ = new Subject<void>();
    this.selfAssessmentInfo = new RequestSaveSelfAssessment();
  }

  ngOnInit(): void {
    const idTramit = this.store.idTramit!;
    const year = this.store.taxYear!;
    this.addRequestPeriodInformation(year.toString());
    this.callCalcul(idTramit);
    this.createSummaryTemplate();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected closeAlert(): void {
    this.alertTitle = undefined;
  }

  private addRequestPeriodInformation(year: string): void {
    this.requestPeriodInfo = {
      impost: Constant.IGEC_NAME,
      year: year,
      period: Constant.IGEC_PERIOD,
      model: Constant.IGEC_MODEL,
    };
  }

  private callCalcul(idTramit: string): void {
    this.calculationService
      .getCalcul(idTramit)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        const calcul: ResponseCalcul = response.content;
        if (calcul) {
          this.allCalculationInfo = calcul;
          this.calculationInfo = {
            quotaTotal: this.allCalculationInfo.quotaTributaria,
            quotaLiquidades:
              this.allCalculationInfo.quotesLiquidadesAnteriorment,
          };
          this.createSummaryTemplate();
        }
      });
  }

  private createSummaryTemplate(): void {
    this.template = [
      {
        translate: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_CALCULATION.TEMPLATE.TAXABLE_BASE',
        ),
        value: this.decimalPipe.transform(
          this.allCalculationInfo?.baseImposable || 0,
          '1.0-0',
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_CALCULATION.TEMPLATE.EXEMPTED_MINIUM',
        ),
        value: this.decimalPipe.transform(
          this.allCalculationInfo?.minExemptos || 0,
          '1.0-0',
        ),
      },
      {
        translate: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_CALCULATION.TEMPLATE.PAYABLE_BASE',
        ),
        value:
          this.allCalculationInfo && this.allCalculationInfo?.baseLiquidable > 0
            ? this.decimalPipe.transform(
                this.allCalculationInfo?.baseLiquidable,
                '1.0-0',
              )
            : '0',
      },
      {
        translate: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_CALCULATION.TEMPLATE.APPLICABLE_RATE',
        ),
        value: this.currencyPipe.transform(
          this.allCalculationInfo?.tipusAplicable || 0,
          'EUR',
          'symbol',
          '1.2-2',
        ),
        lineBreak: true,
      },
      {
        translate: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_CALCULATION.TEMPLATE.TAX_RATE',
        ),
        value:
          this.allCalculationInfo &&
          this.allCalculationInfo?.quotaTributaria > 0
            ? this.currencyPipe.transform(
                this.allCalculationInfo?.quotaTributaria,
                'EUR',
                'symbol',
                '1.2-2',
              )
            : '0,00 €',
      },
    ];
  }

  private formatDates(): void {
    if (this.selfAssessmentInfo?.dataIniciComput) {
      const initComput = this.datePipe.transform(
        this.selfAssessmentInfo.dataIniciComput,
        'yyyy-MM-dd',
      );
      this.selfAssessmentInfo.dataIniciComput = initComput;
    }
    const endComput = this.datePipe.transform(
      this.selfAssessmentInfo.dataFiComput,
      'yyyy-MM-dd',
    );
    const endVoluntary = this.datePipe.transform(
      this.selfAssessmentInfo.dataFiTermini,
      'yyyy-MM-dd',
    );
    this.selfAssessmentInfo.dataFiComput = endComput!;
    this.selfAssessmentInfo.dataFiTermini = endVoluntary!;
  }

  protected handleButtonNext(): void {
    this.disabledButtonNext = !!(
      (this.selfAssessmentInfo?.requerimentAdministracio === null &&
        this.requestPeriodInfo &&
        this.calculationInfo) ||
      (this.totalToPay < 0 && this.header.receiptId)
    );
  }

  private setComplementariaMessageAlert(): void {
    if (this.totalToPay < 0 && this.header.receiptId) {
      // complementaria < 0
      this.alertTitle = this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_CALCULATION.ALERTS.DISABLED_COMPLEMENTARY',
      );
    }
  }

  protected getDataFromTributs(event: Event): void {
    if (!(event as CustomEvent).detail) {
      this.disabledButtonNext = false;
    } else {
      this.selfAssessmentInfo = (event as CustomEvent).detail;
      if (this.selfAssessmentInfo.interessos === 0) {
        this.selfAssessmentInfo.dataIniciComput = null;
      }
      this.selfAssessmentInfo.total = this.getTotalValue();
      this.handleButtonNext();
      this.setComplementariaMessageAlert();
    }
  }

  private getTotalValue(): number {
    const surcharge = +this.selfAssessmentInfo.recarrec ?? 0;
    const liquidades = +this.selfAssessmentInfo.quotasLiquidades ?? 0;
    const interest = +this.selfAssessmentInfo.interessos ?? 0;
    this.totalToPay =
      (this.calculationInfo?.quotaTotal ?? 0) +
      surcharge +
      interest -
      liquidades;

    return this.totalToPay < 0 ? 0 : Number(this.totalToPay.toFixed(2));
  }

  protected submit(): void {
    this.formatDates();
    this.selfAssessmentInfo.idTramit = this.store.idTramit!;
    this.selfAssessmentInfo.model = Constant.IGEC_MODEL;
    this.calculationService
      .saveSelfAssessment(this.selfAssessmentInfo)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((idAutoliquidacio) => {
        if (idAutoliquidacio.content) {
          this.store.selfassessmentId = idAutoliquidacio.content;
          this.store.amountToPay = this.selfAssessmentInfo.total;
          this.router.navigate([AppRoutes.PRESENTATION]);
        }
      });
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.TAX_DECLARATION]);
  }
}
