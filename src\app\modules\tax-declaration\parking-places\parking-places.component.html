<se-panel
  [title]="
    'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.TITLE'
      | translate
  "
  [panelTheme]="'primary'"
  class="parking-places"
>
  <p>
    {{
      "SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.INDICATIONS"
        | translate
    }}
  </p>

  <form [formGroup]="formParkingPlaces" class="row">
    <div class="w-100 d-flex flex-column flex-sm-row">
      <se-input
        class="col-12 col-sm-2 me-0 me-sm-3"
        [decimals]="0"
        [currencySymbol]="''"
        [currencyMode]="true"
        [labelAlign]="'right'"
        formControlName="numberPlaces"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.PLACES_NAME'
            | translate
        "
      ></se-input>
      <se-input
        class="col-12 col-sm-2 me-0 me-sm-3"
        [decimals]="2"
        [currencySymbol]="''"
        [currencyMode]="true"
        [labelAlign]="'right'"
        formControlName="surface"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.SURFACE'
            | translate
        "
      ></se-input>
      <se-dropdown
        class="col-12 col-sm-7 pe-0 pe-sm-4"
        id="situation"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.TYPOLOGY'
            | translate
        "
        formControlName="typology"
        [options]="typologyEstablishmentsForParkingPlaces"
      ></se-dropdown>
    </div>
    <div class="col-12 col-sm-11 pe-auto pe-sm-1">
      <se-alert
        [type]="'info'"
        [closeButton]="false"
        *ngIf="isAlertVisible"
        [title]="messageAlert"
      ></se-alert>
      <se-checkbox
        *ngIf="hasNonCompliance"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.IS_AUTHORIZED'
            | translate
        "
        formControlName="isAuthorized"
      ></se-checkbox>
    </div>
  </form>
</se-panel>
