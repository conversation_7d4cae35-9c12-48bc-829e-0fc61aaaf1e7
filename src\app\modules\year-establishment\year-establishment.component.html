<div class="d-flex flex-column gap-4" *ngIf="idTramit">
  <se-alert
    [title]="
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ALERT_INFO'
        | translate
    "
    [type]="'info'"
  >
  </se-alert>

  <!-- CC-TRIBUTS - YEAR-PERIOD -->
  <mf-tributs-year-period
    #yearPeriod
    *axLazyElement
    [labelDescription]="
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.YEAR_DESCRIPTION'
        | translate
    "
    [impost]="'IGEC'"
    [usePeriod]="false"
    (onChange)="onTaxYearChange($event)"
    [yearValue]="exerciciChange"
  />

  <!-- ESTABLIMENTS -->
  <se-panel
    [id]="'establimentsId'"
    [title]="
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.TITLE'
        | translate
    "
    [colapsible]="false"
    [collapsed]="false"
    [panelTheme]="'primary'"
  >
    <p class="fw-bold">
      {{ descriptionLabel | translate }}
      <ng-icon
        *ngIf="createEstablishmentForm"
        class="tooltip-icon"
        name="matInfo"
        [pTooltipAccessible]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.DESCRIPTION_1_TOOLTIP'
            | translate
        "
      ></ng-icon>
    </p>

    <ng-container *ngIf="establishmentList && establishmentList.length > 0">
      <ng-container *ngTemplateOutlet="listEstablishment"></ng-container>
    </ng-container>
    <ng-container *ngIf="createEstablishmentForm">
      <ng-container *ngTemplateOutlet="createEstablishment"></ng-container>
    </ng-container>
    <ng-container *ngTemplateOutlet="countingMode"></ng-container>
  </se-panel>

  <ng-container *ngIf="isAutomaticComplementaryVisible">
    <mf-tributs-automatic-complementary
      *axLazyElement
      [isOptional]="true"
      [hideTableColumns]="removeComplementaryTableColumns"
      [searchRequestParameters]="searchComplementary"
      (complementaryButtonClick)="submitComplementary($event)"
      (payButtonClick)="onPayButtonClick($event)"
      (automaticSearchEnd)="onAutomaticComplementarySearchEnd($event)"
      (formSearchEnd)="submitComplementary($event)"
      (continueWithoutSearch)="onContinueWithoutSearch($event)"
    />
  </ng-container>

  <!--  BUTTONS -->
  <section class="d-flex justify-content-between flex-row">
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
      {{ 'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate }}
    </se-button>
    <se-button
      *ngIf="isNextButtonVisible"
      [disabled]="isFormIncomplete()"
      (onClick)="submit()"
    >
      {{ 'UI_COMPONENTS.BUTTONS.NEXT' | translate }}
    </se-button>
  </section>
</div>

<ng-template #countingMode>
  <form [formGroup]="componentForm">
    <hr />
    <div class="row">
      <p class="fw-bold">
        {{
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.VEHICLE_COUNTING'
            | translate
        }}
      </p>
    </div>
    <div class="row">
      <div class="col-3">
        <se-radio
          id="id1"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.COUNTING_MODE_1'
              | translate
          "
          [name]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.COUNTING_MODE_1'
              | translate
          "
          [value]="true"
          formControlName="isComptatge"
        ></se-radio>
      </div>
      <div class="col-3">
        <se-radio
          id="id2"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.COUNTING_MODE_2'
              | translate
          "
          [name]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.COUNTING_MODE_2'
              | translate
          "
          [value]="false"
          formControlName="isComptatge"
        ></se-radio>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #listEstablishment>
  <form [formGroup]="componentForm">
    <div class="row align-items-center">
      <div class="col-lg-9">
        <se-dropdown
          [id]="'establishment'"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.ESTABLISHMENT'
              | translate
          "
          [options]="establishmentList"
          [filter]="establishmentList!.length > 10"
          formControlName="establiment"
        ></se-dropdown>
      </div>
      <div class="col-lg-3">
        <se-button
          [btnTheme]="'secondary'"
          (onClick)="handleAddEstablishmentButton()"
        >
          {{ buttonEstablishmentLabel }}
        </se-button>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #createEstablishment>
  <form [formGroup]="createEstablishmentForm!">
    <!-- COMMERCIAL NAME AND CATEGORY -->
    <div class="row">
      <div class="col-lg-6">
        <se-input
          formControlName="nomComercial"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.ESTABLISHMENT'
              | translate
          "
          [type]="'text'"
          [id]="'nomComercial'"
        ></se-input>
      </div>
      <div class="col-lg-6">
        <se-dropdown
          [id]="'categoria'"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.CATEGORY'
              | translate
          "
          [filter]="true"
          [options]="categoriesList"
          formControlName="categoria"
          [tooltipText]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.CATEGORY_TOOLTIP'
              | translate
          "
        ></se-dropdown>
      </div>
    </div>
    <!-- TYPE ADDRESS, ADDRESS NAME, NUMBERING  -->
    <div class="row">
      <div class="col-12 col-sm">
        <se-dropdown
          [id]="'tipusVia'"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.ADDRESS_TYPE'
              | translate
          "
          [filter]="true"
          [options]="addressTypeList"
          formControlName="tipusVia"
        ></se-dropdown>
      </div>
      <div class="col-12 col-sm-5">
        <se-input
          formControlName="nomVia"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.ADDRESS_NAME'
              | translate
          "
          [type]="'text'"
          [id]="'nomVia'"
        ></se-input>
      </div>
      <div class="col-12 col-sm">
        <se-dropdown
          [id]="'tipusNumeracio'"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.NUMBERING_TYPE'
              | translate
          "
          [options]="numberingTypeList"
          formControlName="tipusNumeracio"
        ></se-dropdown>
      </div>
      <div class="col-12 col-sm">
        <se-input
          formControlName="num"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.NUMBERING'
              | translate
          "
          [type]="'number'"
          [id]="'num'"
        ></se-input>
      </div>
    </div>
    <!-- LETTER, SCALE, FLOOR, DOOR, POSTAL CODE, PROVINCE, MUNICIPALITY -->
    <div class="row">
      <div class="col-12 col-sm">
        <se-input
          formControlName="lletra"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.LETTER'
              | translate
          "
          [type]="'text'"
          [id]="'lletra'"
          class="space"
        ></se-input>
      </div>
      <div class="col-12 col-sm">
        <se-input
          formControlName="escala"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.SCALE'
              | translate
          "
          [type]="'text'"
          [id]="'escala'"
          class="space"
        ></se-input>
      </div>
      <div class="col-12 col-sm">
        <se-input
          formControlName="pis"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.FLOOR'
              | translate
          "
          [type]="'text'"
          [id]="'pis'"
          class="space"
        ></se-input>
      </div>
      <div class="col-12 col-sm">
        <se-input
          formControlName="portal"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.DOOR'
              | translate
          "
          [type]="'text'"
          [id]="'portal'"
          class="space"
        ></se-input>
      </div>
      <div class="col-12 col-sm-2">
        <se-input
          formControlName="codiPostal"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.POSTAL_CODE'
              | translate
          "
          [type]="'text'"
          [id]="'codiPostal'"
          (onBlur)="onBlurPostalCode($event)"
        ></se-input>
      </div>
      <div class="col-12 col-sm-3">
        <se-dropdown
          [id]="'provincia'"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.PROVINCE'
              | translate
          "
          [options]="provinceList"
          formControlName="provincia"
        ></se-dropdown>
      </div>
      <div class="col-12 col-sm-3">
        <se-dropdown
          [id]="'municipi'"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.MUNICIPALITY'
              | translate
          "
          [options]="municipyList"
          formControlName="municipi"
        ></se-dropdown>
      </div>
    </div>
  </form>
</ng-template>
