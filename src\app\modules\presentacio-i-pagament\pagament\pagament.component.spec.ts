import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PagamentComponent } from './pagament.component';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('PagamentComponent', () => {
  let component: PagamentComponent;
  let fixture: ComponentFixture<PagamentComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateModule.forRoot(), HttpClientTestingModule],
      declarations: [PagamentComponent],
    });
    fixture = TestBed.createComponent(PagamentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
