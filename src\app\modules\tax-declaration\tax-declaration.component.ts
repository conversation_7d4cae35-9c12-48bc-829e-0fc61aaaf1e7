import { Component, OnInit, type OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { DecimalPipe } from '@angular/common';
import { Subject, forkJoin, map, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { Row, SeDropdownOption } from 'se-ui-components-mf-lib';

import { AppRoutes, StoreService } from 'src/app/core';
import { TaxDeclarationService } from './tax-declaration-endpoints.service';
import { TypologyResponse } from './models/typology.model';
import { CoefficientResponse } from './models/coefficient-corrector.model';
import {
  CountDeclarationData,
  NonCountDeclarationData,
  ParkingSpaceDeclaration,
  TaxDeclaration,
} from './models/tax-declaration.model';

@Component({
  selector: 'app-tax-declaration',
  styleUrls: ['tax-declaration.component.scss'],
  templateUrl: './tax-declaration.component.html',
})
export class TaxDeclarationComponent implements OnD<PERSON>roy, OnInit {
  protected isCountingModeWithInstrument!: boolean;
  protected idTramit!: string;
  protected categoryId!: string;
  protected typologyList: TypologyResponse[] = [];
  protected typologyEstablishmentsForParkingPlaces: SeDropdownOption[] = [];
  protected typologyPlacesForCoefficientCorrectorModal: string[] = [];
  protected coefficientRowList: Row[] = [];
  protected coefficientList: CoefficientResponse[] = [];
  protected exemptMinimum!: number;
  protected ratio!: number;
  protected coefficient!: number;
  protected isDisableTaxDeclarationForm = true;
  protected isSubmitButtonDisable = true;
  protected establishmentSituationList: SeDropdownOption[] = [];
  protected nonCountDeclarationData: NonCountDeclarationData | undefined;
  protected countDeclarationData: CountDeclarationData | undefined;
  protected parkingSpaceDeclaration!: ParkingSpaceDeclaration;

  private taxDeclaration!: TaxDeclaration;
  private destroyed$: Subject<void> = new Subject<void>();

  constructor(
    private store: StoreService,
    private router: Router,
    private taxDeclarationService: TaxDeclarationService,
    private translateService: TranslateService,
    private decimalPipe: DecimalPipe
  ) {}

  ngOnInit(): void {
    this.isCountingModeWithInstrument =
      this.store.isCountingModeWithInstrument!;
    this.idTramit = this.store.idTramit!;

    if (this.idTramit) {
      if (this.isCountingModeWithInstrument) {
        this.setTaxDeclarationWithInstrument();
      } else {
        this.setTaxDeclarationWithoutInstrument();
      }
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.YEAR_ESTABLISHMENT]);
  }

  protected handleDisableSubmitButton(isDisable: boolean): void {
    this.isSubmitButtonDisable = isDisable;
  }

  protected handleChangeMinimumVehicleExemption(minimum: number): void {
    this.taxDeclaration = { ...this.taxDeclaration, minimExempt: minimum };
  }

  protected handleChangeTaxableBaseNonCountDeclarationData({
    base,
    openingDays,
    situation,
    surface,
  }: NonCountDeclarationData): void {
    this.taxDeclaration = {
      ...this.taxDeclaration,
      baseImposable: base,
      diesObertura: openingDays,
      situacionEstabliment: situation,
      superficieVenta: +surface,
      ratioVehicles: this.ratio,
    };
  }

  protected handleChangeTaxableBaseCountDeclarationData({
    base,
    numberVehicles,
    vehiclesExcluded,
  }: CountDeclarationData): void {
    this.taxDeclaration = {
      ...this.taxDeclaration,
      baseImposable: base,
      numVehicles: +numberVehicles,
      numVehiclesExclosos: +vehiclesExcluded,
    };
  }

  protected handleSaveParkingPlaces({
    numberPlaces,
    typology,
    surface,
    isAuthorized,
    coefficient,
  }: ParkingSpaceDeclaration): void {
    if (coefficient) {
      this.taxDeclaration = {
        ...this.taxDeclaration,
        coeficienteCorrector: coefficient,
      };
      this.coefficient = coefficient;
    }

    if (isAuthorized !== null) {
      this.taxDeclaration = {
        ...this.taxDeclaration,
        autorizatplacesPerBarem: isAuthorized,
      };

      this.coefficient = isAuthorized
        ? 1
        : this.taxDeclaration.coeficienteCorrector!;
    }

    if (typology && surface && numberPlaces) {
      this.taxDeclaration = {
        ...this.taxDeclaration,
        numPlaces: numberPlaces,
        tipologia: this.typologyList.find(({ valor }) => valor === typology)
          ?.codigo,
        superficieVenta: surface,
      };
      this.isDisableTaxDeclarationForm = false;
    }
  }

  protected handleDisableDeclarationDataForm(): void {
    this.isDisableTaxDeclarationForm = true;
  }

  protected onSubmit(): void {
    const body: TaxDeclaration = {
      ...this.taxDeclaration,
      idTramit: this.idTramit,
      coeficienteCorrector: this.coefficient,
    };

    this.taxDeclarationService
      .saveTaxDeclaration(body)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: (response) => {
          if (response?.content) {
            this.store.declared = true;
            this.router.navigate([AppRoutes.SUMMARY_CALCULATION]);
          }
        },
      });
  }

  private setTypology(typologyList: TypologyResponse[]): void {
    this.typologyList = typologyList;

    this.typologyEstablishmentsForParkingPlaces = typologyList
      .filter(({ mostrar }) => mostrar)
      .map(({ descripcio, valor }) => ({
        label: this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.ESTABLISHMENT_PLACE_PER_SALES',
          { description: descripcio, value: valor }
        ),
        id: valor,
      }));

    this.typologyPlacesForCoefficientCorrectorModal = typologyList.map(
      ({ descripcio, valor }) =>
        this.translateService.instant(
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.PARKING_PLACES.ESTABLISHMENT_PLACE',
          { description: descripcio, value: valor }
        )
    );
  }

  private getEstablishmentSituations(
    establishmentSituationList: CoefficientResponse[]
  ): SeDropdownOption[] {
    return establishmentSituationList?.map(
      ({ descripcio: label, valor: id }) => ({
        label,
        id,
      })
    );
  }

  private setTaxDeclarationResponseWithoutInstrument(
    response: TaxDeclaration
  ): void {
    const {
      baseImposable,
      diesObertura,
      superficieVenta,
      situacionEstabliment,
    } = response;
    this.nonCountDeclarationData = {
      base: baseImposable,
      openingDays: diesObertura!,
      surface: superficieVenta!,
      situation: +situacionEstabliment!,
    };
  }

  private setTaxDeclarationResponseWithInstrument(
    response: TaxDeclaration
  ): void {
    const {
      baseImposable,
      numVehicles,
      numVehiclesExclosos,
      autorizatplacesPerBarem,
      tipologia,
      numPlaces,
      superficieVenta,
      coeficienteCorrector,
    } = response;
    this.countDeclarationData = {
      base: baseImposable,
      numberVehicles: numVehicles!,
      vehiclesExcluded: numVehiclesExclosos!,
    };

    this.parkingSpaceDeclaration = {
      isAuthorized: autorizatplacesPerBarem,
      typology: this.typologyList.find(({ codigo }) => codigo === tipologia)
        ?.valor,
      numberPlaces: numPlaces,
      surface: superficieVenta,
      coefficient: coeficienteCorrector,
    };
  }

  private setTaxDeclarationWithInstrument(): void {
    const sources = [
      this.taxDeclarationService.getExemptMinimum(this.idTramit),
      this.taxDeclarationService.getCoefficients(this.idTramit),
      this.taxDeclarationService.getTypology(this.idTramit),
      this.taxDeclarationService.getTaxDeclaration(this.idTramit),
    ];

    forkJoin(sources)
      .pipe(
        takeUntil(this.destroyed$),
        map(([minimum, coefficients, typology, taxDeclaration]) => ({
          exemptMinimum: minimum.content,
          coefficients: coefficients.content,
          typology: typology.content,
          taxDeclaration: taxDeclaration.content,
        }))
      )
      .subscribe(
        ({ exemptMinimum, coefficients, typology, taxDeclaration }) => {
          this.exemptMinimum =
            (taxDeclaration as TaxDeclaration)?.minimExempt ??
            (exemptMinimum as number);
          if ((coefficients as CoefficientResponse[]).length) {
            this.coefficientList = coefficients as CoefficientResponse[];
            this.coefficientRowList = this.getFormatCoefficientListWithDecimals(
              coefficients as CoefficientResponse[]
            );
          }
          if ((typology as TypologyResponse[]).length) {
            this.setTypology(typology as TypologyResponse[]);
          }
          if (taxDeclaration) {
            this.setTaxDeclarationResponseWithInstrument(
              taxDeclaration as TaxDeclaration
            );
          }
        }
      );
  }

  private setTaxDeclarationWithoutInstrument(): void {
    const sources = [
      this.taxDeclarationService.getExemptMinimum(this.idTramit),
      this.taxDeclarationService.getEstablishmentSituations(this.idTramit),
      this.taxDeclarationService.getRatio(this.idTramit),
      this.taxDeclarationService.getTaxDeclaration(this.idTramit),
    ];
    forkJoin(sources)
      .pipe(
        takeUntil(this.destroyed$),
        map(([minimum, establishmentSituations, ratio, taxDeclaration]) => ({
          exemptMinimum: minimum.content,
          establishmentSituations: establishmentSituations.content,
          ratio: ratio.content,
          taxDeclaration: taxDeclaration.content,
        }))
      )
      .subscribe(
        ({ exemptMinimum, establishmentSituations, ratio, taxDeclaration }) => {
          this.exemptMinimum =
            (taxDeclaration as TaxDeclaration)?.minimExempt ??
            (exemptMinimum as number);
          this.ratio = ratio as number;
          if ((establishmentSituations as CoefficientResponse[]).length) {
            this.establishmentSituationList = this.getEstablishmentSituations(
              establishmentSituations as CoefficientResponse[]
            );
          }
          if (taxDeclaration) {
            this.setTaxDeclarationResponseWithoutInstrument(
              taxDeclaration as TaxDeclaration
            );
          }
        }
      );
  }

  private getFormatCoefficientListWithDecimals(
    coefficients: CoefficientResponse[]
  ): Row[] {
    return coefficients.map((data) => ({
      data: {
        descripcio: {
          value: data?.descripcio,
        },
        valor: {
          value: this.decimalPipe.transform(data?.valor || 0, '1.0-3'),
          cellConfig: { align: 'right' },
        },
      },
    }));
  }
}
