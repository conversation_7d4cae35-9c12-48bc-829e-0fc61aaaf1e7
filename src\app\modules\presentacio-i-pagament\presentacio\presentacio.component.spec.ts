import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PresentacioComponent } from './presentacio.component';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('PresentacioComponent', () => {
  let component: PresentacioComponent;
  let fixture: ComponentFixture<PresentacioComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateModule.forRoot(), HttpClientTestingModule],
      declarations: [PresentacioComponent],
    });
    fixture = TestBed.createComponent(PresentacioComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
