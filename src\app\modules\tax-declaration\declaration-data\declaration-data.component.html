<se-panel [title]="declarationTitle" [panelTheme]="'primary'">
  <h5 *ngIf="isCountingModeWithInstrument">
    {{
      'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.SUB_TITLE'
        | translate
    }}
  </h5>
  <p>
    {{ declarationIndication }}
  </p>

  <aside
    class="declaration-data__vehicles"
    *ngIf="isCountingModeWithInstrument"
  >
    <form
      [formGroup]="formCorrectorCoefficient"
      *ngIf="formCorrectorCoefficient"
      class="d-flex align-items-end flex-column flex-sm-row w-100"
    >
      <p><strong>(</strong></p>

      <se-input
        formControlName="numberVehicles"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.VEHICLES_NUMBER'
            | translate
        "
        [min]="1"
        [max]="99999999"
        [maxLength]="8"
        [decimals]="0"
        [labelAlign]="'right'"
        [currencySymbol]="''"
        [currencyMode]="true"
        class="col-12 col-sm-2"
        [tooltipText]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.VEHICLES_TOOLTIP'
            | translate
        "
      />

      <p><strong>-</strong></p>

      <se-input
        formControlName="vehiclesExcluded"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.VEHICLES_EXCLUDED'
            | translate
        "
        [min]="0"
        [max]="99999999"
        [maxLength]="8"
        [decimals]="0"
        [currencySymbol]="''"
        [labelAlign]="'right'"
        [currencyMode]="true"
        [tooltip]="true"
        class="col-12 col-sm-3"
        [tooltipText]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.VEHICLES_EXCLUDED_TOOLTIP'
            | translate
        "
      />

      <p><strong>)</strong></p>

      <p><strong>x</strong></p>

      <se-input
        formControlName="correctionCoefficient"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.CORRECTION_COEFFICIENT'
            | translate
        "
        [decimals]="3"
        [currencySymbol]="''"
        [currencyMode]="true"
        [labelAlign]="'right'"
        [tooltip]="true"
        class="col-12 col-sm-2"
        [tooltipText]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.CORRECTION_COEFFICIENT_TOOLTIP'
            | translate
        "
      />

      <p><strong>=</strong></p>

      <se-input
        formControlName="taxableBase"
        [labelAlign]="'right'"
        [decimals]="0"
        [currencySymbol]="''"
        [currencyMode]="true"
        [label]="
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_FORM.TAXABLE_BASE'
            | translate
        "
        class="col-12 col-sm-3"
      />
    </form>

    <se-button
      (onClick)="handleOpenCorrectionCoefficientInfo()"
      [icon]="'matInfo'"
      [btnTheme]="'onlyText'"
    >
      {{
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.WHAT_IS_CORRECTION_COEFFICIENT'
          | translate
      }}
    </se-button>
  </aside>

  <aside
    class="declaration-data__establishment"
    *ngIf="!isCountingModeWithInstrument"
  >
    <form [formGroup]="formEstablishment">
      <div class="d-flex">
        <se-input
          class="col-12 col-sm-3"
          [decimals]="4"
          [currencySymbol]="''"
          [currencyMode]="true"
          [labelAlign]="'right'"
          formControlName="ratio"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_FOR_DECLARATION.RATIO'
              | translate
          "
        ></se-input>
      </div>
      <div
        class="declaration-data__establishment--editable-section d-flex flex-column flex-sm-row"
      >
        <se-input
          class="col-12 col-sm-3"
          [decimals]="2"
          [currencySymbol]="''"
          [currencyMode]="true"
          [min]="0"
          [labelAlign]="'right'"
          formControlName="surface"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_FOR_DECLARATION.SURFACE'
              | translate
          "
        ></se-input>
        <se-input
          class="col-12 col-sm-3"
          [decimals]="0"
          [currencySymbol]="''"
          [currencyMode]="true"
          [labelAlign]="'right'"
          formControlName="openingDays"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_FOR_DECLARATION.OPENING_DAYS'
              | translate
          "
        ></se-input>
        <se-dropdown
          class="col-12 col-sm-3"
          id="situation"
          *ngIf="establishmentSituationList.length"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_FOR_DECLARATION.SITUATION'
              | translate
          "
          formControlName="situation"
          [options]="establishmentSituationList"
        ></se-dropdown>
      </div>
      <div class="d-flex">
        <se-input
          class="col-12 col-sm-3"
          [decimals]="0"
          [currencySymbol]="''"
          [currencyMode]="true"
          [labelAlign]="'right'"
          formControlName="base"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_FOR_DECLARATION.BASE'
              | translate
          "
          [tooltipText]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_FOR_DECLARATION.BASE_TOOLTIP'
              | translate
          "
        ></se-input>
      </div>
    </form>
  </aside>

  <ng-container *ngIf="formVehiclesExemption">
    <hr />

    <aside class="declaration-data__exempt-minimum">
      <h5>
        {{
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.EXEMPT_MINIMUM'
            | translate
        }}
      </h5>
      <p>
        {{
          'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.EXEMPT_MINIMUM_DESCRIPTION'
            | translate
        }}
      </p>
      <form [formGroup]="formVehiclesExemption" class="row">
        <se-input
          formControlName="minimumVehicles"
          [label]="
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.VEHICLES_MIN'
              | translate
          "
          [labelAlign]="'right'"
          [min]="0"
          [decimals]="0"
          [currencySymbol]="''"
          [currencyMode]="true"
          [max]="exemptMinimumValue"
          class="col-12 col-sm-4"
        ></se-input>
        <se-button
          class="col-12 col-sm-3"
          [disabled]="formVehiclesExemption.enabled"
          (onClick)="onEnableMinimumVehicleExemption()"
          [btnTheme]="'secondary'"
          >{{
            'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.MODIFY'
              | translate
          }}</se-button
        >
      </form>
    </aside>
  </ng-container>
</se-panel>
