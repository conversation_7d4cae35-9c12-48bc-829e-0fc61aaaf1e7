import { SeHttpResponse } from 'se-ui-components-mf-lib';

import { EstablishmentCode } from './typology.model';

export interface NonCountDeclarationData {
  base: number;
  openingDays: number;
  situation: number;
  surface: number;
}

export interface CountDeclarationData {
  base: number;
  numberVehicles: number;
  vehiclesExcluded: number;
}

export interface ParkingSpaceDeclaration {
  numberPlaces?: number;
  surface?: number;
  typology?: number;
  coefficient?: number;
  isAuthorized?: boolean;
}

export interface TaxDeclaration {
  idTramit: string;
  minimExempt: number;
  baseImposable: number;
  superficieVenta?: number;

  numPlaces?: number;
  tipologia?: EstablishmentCode;
  autorizatplacesPerBarem?: boolean;
  numVehicles?: number;
  numVehiclesExclosos?: number;
  coeficienteCorrector?: number;

  ratioVehicles?: number;
  diesObertura?: number;
  situacionEstabliment?: number;
}

export interface PostTaxDeclarationResponse extends SeHttpResponse {
  content?: {
    idTramit: string;
  };
}

export interface GetTaxDeclarationResponse extends SeHttpResponse {
  content?: TaxDeclaration;
}
