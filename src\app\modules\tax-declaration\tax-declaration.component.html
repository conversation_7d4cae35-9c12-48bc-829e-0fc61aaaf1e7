<se-alert
  [type]="'info'"
  [title]="
    'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.ALERTS.INFO' | translate
  "
  [closeButton]="true"
>
</se-alert>

<div class="mb-4" *ngIf="isCountingModeWithInstrument">
  <app-parking-places
    [coefficientList]="coefficientList"
    [typologyEstablishmentsForParkingPlaces]="
      typologyEstablishmentsForParkingPlaces
    "
    [parkingSpaceDeclaration]="parkingSpaceDeclaration"
    (handleSaveParkingPlaces)="handleSaveParkingPlaces($event)"
    (handleDisableDeclarationDataForm)="handleDisableDeclarationDataForm()"
  ></app-parking-places>
</div>
<app-declaration-data
  [isCountingModeWithInstrument]="isCountingModeWithInstrument"
  [typologyPlacesForCoefficientCorrectorModal]="
    typologyPlacesForCoefficientCorrectorModal
  "
  [tableBodyCoefficientCorrector]="coefficientRowList"
  [exemptMinimum]="exemptMinimum"
  [establishmentSituationList]="establishmentSituationList"
  [ratio]="ratio"
  [coefficient]="coefficient"
  [isDisableTaxDeclarationForm]="isDisableTaxDeclarationForm"
  [nonCountDeclarationData]="nonCountDeclarationData"
  [countDeclarationData]="countDeclarationData"
  (handleChangeMinimumVehicleExemption)="
    handleChangeMinimumVehicleExemption($event)
  "
  (handleChangeTaxableBaseNonCountDeclarationData)="
    handleChangeTaxableBaseNonCountDeclarationData($event)
  "
  (handleChangeTaxableBaseCountDeclarationData)="
    handleChangeTaxableBaseCountDeclarationData($event)
  "
  (handleDisableSubmitButton)="handleDisableSubmitButton($event)"
></app-declaration-data>

<section class="mt-3 d-flex justify-content-between flex-row">
  <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
    {{ 'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate }}
  </se-button>
  <se-button [disabled]="isSubmitButtonDisable" (onClick)="onSubmit()">
    {{ 'UI_COMPONENTS.BUTTONS.NEXT' | translate }}
  </se-button>
</section>
