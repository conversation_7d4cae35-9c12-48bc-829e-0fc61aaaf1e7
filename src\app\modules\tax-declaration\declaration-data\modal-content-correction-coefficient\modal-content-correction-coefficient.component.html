<se-modal
  [data]="data"
  (modalOutputEvent)="closeModal()"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <div class="tooltip-content">
    <div class="tooltip-content__typology">
      <p *ngIf="typologyPlacesForCoefficientCorrectorModal.length">
        {{
          "SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.LOCATIONS_BY_COMMERCIAL_TYPOLOGY_MODAL_DATA"
            | translate
        }}
      </p>
      <ul>
        <li
          *ngFor="
            let place of typologyPlacesForCoefficientCorrectorModal;
            let i = index
          "
          [id]="'place' + i"
        >
          {{ place }}
        </li>
      </ul>
    </div>

    <hr *ngIf="tableBodyData.length" />

    <div class="tooltip-content__table" *ngIf="tableBodyData.length">
      <se-table
        [columns]="headerColumns"
        [data]="tableBodyData"
        [resizable]="true"
        [cellTemplatePriorityOrder]="'row-column-cell'"
      ></se-table>
    </div>
  </div>
</se-modal>
