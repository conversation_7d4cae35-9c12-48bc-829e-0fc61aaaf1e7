import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  Nullable,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  RequestReadProvinceMunicipality,
  Tramit,
  DadesInicialsResponse,
  WorkingSessionResponse,
} from './year-establishment.model';

@Injectable({
  providedIn: 'root',
})
export class YearEstablishmentEndpointService {
  constructor(private httpService: SeHttpService) {
    //Intencionadamente vacío
  }

  /** GET - Search for existing establishments */
  getEstablishment(idTramit: string): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/establiments`,
      clearExceptions: true,
    });
  }

  /**GET - DROPDOWN NUMBERING TYPE /api/dades-referencia/tipus-numeracio  */
  getNumberingType(): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/tipus-numeracio`,
      clearExceptions: true,
    });
  }

  /** GET - DROPDOWN ADDRESS TYPE GET /api/dades-referencia/tipus-via */
  getAddressType(): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/tipus-via`,
      clearExceptions: true,
    });
  }

  /** GET - DROPDOWN CODE POSTAL /api/dades-referencia/codi-postal/:cp */
  getCode(cp: string) {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/codi-postal/${cp}`,
      clearExceptions: true,
    });
  }

  /** GET - DROPDOWN PROVINCE /api/dades-referencia/codi-postal/:cp/provincia */
  getProvinces(cp: string) {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/codi-postal/${cp}/provincia`,
      clearExceptions: true,
    });
  }

  /** GET - DROPDOWN MUNICIPALITY /api/dades-referencia/codi-postal/:cp/municipi */
  getMunicipality(cp: string) {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/codi-postal/${cp}/municipi`,
      clearExceptions: true,
    });
  }

  /** GET - LIST OF PROVINCES AND MUNICIPALITY BY CP */
  getProviceAndMunicipalityList(
    request: RequestReadProvinceMunicipality
  ): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/codi-postal/${request.ca}/${request.postalCode}/${request.indAsList}/data`,
      clearExceptions: true,
    });
  }

  /** GET - DROPDOWN CATEGORIES */
  getCategories(): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDadesReferencia,
      url: `/grans-establiments`,
      clearExceptions: true,
    });
  }

  getInitialData(idTramit: string): Observable<DadesInicialsResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/dades-inicials`,
      clearExceptions: true,
    });
  }

  postInitialData(tramit: Tramit): Observable<SeHttpResponse> {
    return this.httpService.get({
      method: 'post',
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/dades-inicials`,
      clearExceptions: true,
      body: tramit,
    });
  }

  getWorkingSession(
    idProcedure: Nullable<string>,
    yearEstablishment: Nullable<string>,
    clau: string
  ): Observable<WorkingSessionResponse> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlTributs,
      url: `/sessions-treball/${idProcedure}/${yearEstablishment}/${clau}`,
      clearExceptions: true,
    });
  }
}
