import { CommonModule, DecimalPipe } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { NgIconsModule } from '@ng-icons/core';
import { TooltipModule } from 'primeng/tooltip';
import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeDropdownModule,
  SeInputModule,
  SeModalModule,
  SePanelModule,
  SeTableModule,
} from 'se-ui-components-mf-lib';

import { TaxDeclarationComponent } from './tax-declaration.component';
import { DeclarationDataComponent } from './declaration-data/declaration-data.component';
import { ModalContentCorrectionCoefficientComponent } from './declaration-data/modal-content-correction-coefficient/modal-content-correction-coefficient.component';
import { ParkingPlacesComponent } from './parking-places/parking-places.component';

const routes: Routes = [
  {
    path: '',
    component: TaxDeclarationComponent,
    data: {
      title: 'SE_GRANS_ESTABLIMENTS_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP3',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [
    TaxDeclarationComponent,
    DeclarationDataComponent,
    ModalContentCorrectionCoefficientComponent,
    ParkingPlacesComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    SePanelModule,
    TooltipModule,
    SeInputModule,
    SeButtonModule,
    SeModalModule,
    NgbModalModule,
    SeTableModule,
    NgIconsModule,
    SeDropdownModule,
    SeCheckboxModule,
    SeAlertModule,
  ],
  providers: [DecimalPipe],
})
export class TaxDeclarationModule {}
