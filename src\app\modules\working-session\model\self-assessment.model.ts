import { Cell } from "se-ui-components-mf-lib";

interface SelfAssessmentError {
  code: string;
  date: string;
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

interface SubjectePassiu {
  nif: string;
  nom: string;
}

export enum SelfAssessmentEstat {
  GENERAT = 'GENERAT',
  ESBORRANY = 'ESBORRANY',
  NO_PRESENTAT = 'NO_PRESENTAT',
  PENDENT_PRESENTACIO = 'PENDENT_PRESENTACIO',
  PRESENTANT = 'PRESENTANT',
  PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
  PRESENTAT = 'PRESENTAT',
  PAGAT = 'PAGAT',
  PAGANT = 'PAGANT',
  PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
  PAGAMENT_ERROR = 'PAGAMENT_ERROR',
  TRAMITAT = 'TRAMITAT',
  TRAMITANT = 'TRAMITANT',
  TRAMITACIO_ERROR = 'TRAMITACIO_ERROR',
  CONSOLIDANT = 'CONSOLIDANT',
  CONSOLIDAT = 'CONSOLIDAT',
  CONSOLIDACIO_ERROR = 'CONSOLIDACIO_ERROR',
  NOTIFICACIO_ERROR = 'NOTIFICACIO_ERROR',
  ERROR = 'ERROR',
}

export interface SelfAssessment {
  idMfpt: string;
  errors: SelfAssessmentError[];
  numJustificant: string;
  idDocuments: string[];
  idAutoliquidacio: string;
  quotaLiquida: number;
  quotaTributaria: number;
  subjectePassiu: SubjectePassiu;
  tipus: string;
  estat: SelfAssessmentEstat;
  dataPresentacio: string;
}

export interface SelfAssessmentCell {
  numJustificant: Cell;
  idDocuments: Cell;
  idAutoliquidacio: Cell;
  quotaLiquida: Cell;
  quotaTributaria: Cell;
  subjectePassiu: Cell;
  tipus: Cell;
  estat: Cell;
  dataPresentacio: Cell;
  idMfpt?: Cell;
  errors?: Cell;
  actions?: Cell;
}