import { StatusAutoliquidacio } from './status-autoliquidacio.model';
import { SubjectePassiu } from './subjecte-passiu.model';

export interface AutoliquidacioError {
  code: string;
  date: string; // Date
  description: string;
  technicalCode: string;
  technicalDescription: string;
  trackingId: string;
  stackTrace: string;
}

export interface Autoliquidacio {
  idAutoliquidacio: string;
  idDocuments: string[];
  numJustificant: string;
  quotaLiquida: number;
  subjectePassiu: SubjectePassiu;
  tipus: null;
  estat: StatusAutoliquidacio;//PRESENTAT
  dataPresentacio: null;
  dataIncompliment: null;
  dataFinTermini: null;
  dataTerminiPresentacio: null;
  errors?: AutoliquidacioError[];
}
