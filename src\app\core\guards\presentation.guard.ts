import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { StoreService } from '../services';
import { AppRoutes } from '../models';

@Injectable({ providedIn: 'root' })
export class PresentationGuard {
  constructor(private router: Router, private store: StoreService) {}

  canActivate() {
    if (this.store.selfassessmentId) return true;
    this.router.navigate([AppRoutes.SUMMARY_CALCULATION]);
    return false;
  }
}