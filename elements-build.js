const fs = require("fs-extra");
const concat = require("concat");
var pjson = require("./package.json");

(async function build() {
  // Reset previous version
  await fs.emptyDir("./elements");

  // Concatenate Webcomponent JS files
  const files = [];

  await fs.ensureDir("./dist/se-grans-establiments-mf");
  const files1 = await fs.readdir("./dist/se-grans-establiments-mf/");
  files1
    .filter((file) => file.match(/(\w*)\.js$/))
    .forEach(function (file) {
      // Do whatever you want to do with the file
      //console.log(file)
      files.push("./dist/se-grans-establiments-mf/" + file);
    });

  await fs.ensureDir("./elements");
  await concat(files, "./elements/se-grans-establiments.js");

  // Copy styles
  await fs.copyFile(
    "./dist/se-grans-establiments-mf/styles.css",
    "./elements/styles.css"
  );

  // Copy assets
  await fs.ensureDir("./elements/assets");
  await fs.copy("./dist/se-grans-establiments-mf/assets", "./elements/assets");

  var buildtime = new Date();
  var versionJson =
    '{"build":{"version":"' +
    pjson.version +
    '","name":"' +
    pjson.name +
    '","time":"' +
    buildtime.toISOString() +
    '"}}';
  fs.writeFileSync("./elements/version.json", versionJson);
})();
