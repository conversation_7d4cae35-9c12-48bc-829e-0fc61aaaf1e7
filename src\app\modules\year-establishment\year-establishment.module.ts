import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import {
  SeButtonModule,
  SeDropdownModule,
  SeInputModule,
  SePanelModule,
  SeRadioModule,
  SeTooltipAccessibleModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { YearEstablishmentComponent } from './year-establishment.component';
import { NgIconsModule } from '@ng-icons/core';
import { TooltipModule } from 'primeng/tooltip';

const routes: Routes = [
  {
    path: '',
    component: YearEstablishmentComponent,
    data: {
      title: 'SE_GRANS_ESTABLIMENTS_MF.APP_TITLE',
      stepId: 'REPOSITION_RESOURCE_DESKTOP_STEP2',
      stepperId: 'REPOSITION_RESOURCE_ENABLE',
    },
  },
];

@NgModule({
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [YearEstablishmentComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-tributs-year-period',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
        {
          tag: 'mf-tributs-automatic-complementary',
          url: environment.mfTributsURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    FormsModule,
    ReactiveFormsModule,
    SePanelModule,
    SeInputModule,
    SeDropdownModule,
    SeRadioModule,
    SeButtonModule,
    TooltipModule,
    NgIconsModule,
    SeTooltipAccessibleModule,
  ],
})
export class YearEstablishmentModule {}
