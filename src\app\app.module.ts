import { registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClientModule,
} from '@angular/common/http';
import localeCa from '@angular/common/locales/ca';
import {
  APP_INITIALIZER,
  ApplicationRef,
  DoBootstrap,
  Injector,
  LOCALE_ID,
  NgModule,
  isDevMode,
} from '@angular/core';
import { createCustomElement } from '@angular/elements';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';

import {
  SeExceptionViewerModule,
  SeHeaderInfoModule,
  SeHttpInterceptorService,
  SeStepperdModule
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { PrimeNGConfig } from 'primeng/api';
import { Observable, lastValueFrom, take } from 'rxjs';

export function HttpLoaderFactory(httpBackend: HttpBackend) {
  return new MultiTranslateHttpLoader(httpBackend, [
    { prefix: `${environment.baseUrlMf}/assets/i18n/`, suffix: '.json' },
    { prefix: `${environment.baseUrCommons}/i18n/`, suffix: '.json' },
  ]);
}

export function appInitializerFactory(translate: TranslateService, primeNGConfig: PrimeNGConfig): () => Promise<unknown> {
  return () => {
		translate.addLangs(['ca', 'es']);
		translate.setDefaultLang('ca');

    let translations$: Observable<any> = translate.use('ca');

		if (window.location.href.includes('/es/')) {
			translations$ = translate.use('es');
		}

    // Set primeNG specific tranlsations
    translate.get('SE_COMPONENTS.PRIMENG')
    .pipe(take(1)).subscribe(res => primeNGConfig.setTranslation(res));

		return lastValueFrom(translations$);
	};
}

registerLocaleData(localeCa, 'ca');

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpBackend],
      },
    }),
    SeStepperdModule,
    SeExceptionViewerModule,
    SeHeaderInfoModule,
  ],
  providers: [
    { provide: LOCALE_ID, useValue: 'ca-ES' },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SeHttpInterceptorService,
      multi: true,
    },
    CookieService,
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, PrimeNGConfig],
      multi: true,
    },
  ],
  bootstrap: [],
})
export class AppModule implements DoBootstrap {
  constructor(private injector: Injector) {}

  ngDoBootstrap(appRef: ApplicationRef): void {
    const el = createCustomElement(AppComponent, { injector: this.injector });

    // Same name inside concat elements-build.js
    if (!customElements.get('se-grans-establiments')) {
      customElements.define('se-grans-establiments', el);
    }

    if (isDevMode()) {
      appRef.bootstrap(AppComponent);
    }
  }
}
