<div *ngIf="paymentData">
  <mf-pagaments-proces-pagament *axLazyElement 
    [paymentData]="paymentData" 
    [totalDue]="totalAmount" 
    (primaryClicked)="handlePayButton($event)"
    [primaryButtonLabel]="presentacioMode ? 'SE_PAGAMENTS_MF.PROCES_PAGAMENT.PRESENT_PAY' : 'SE_PAGAMENTS_MF.PROCES_PAGAMENT.PAY'"
    [secondaryButtonLabel]="'UI_COMPONENTS.BUTTONS.BACK' | translate" 
    (secondaryClicked)="goBack()"
    [presentacioMode]="presentacioMode"
    [presentacionModal]="progressModal"
    (onPresentationModal)="onOpenPresentationModal($event)">
  </mf-pagaments-proces-pagament>
</div>
