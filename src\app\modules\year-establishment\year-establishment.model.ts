import type { Nullable, SeHttpResponse } from 'se-ui-components-mf-lib';

export interface Establishment {
    descripcio: string | null;
    uuid: string | null;
    id: string;
    nomComercial: string;
    categoria: string;
    categoriaLabel: string;
    tipusVia: string;
    tipusViaLabel: string;
    nomVia: string;
    tipusNumeracio: string;
    tipusNumeracioLabel: string;
    num: number;
    lletra: string;
    escala: string;
    pis: string;
    portal: string;
    codiPostal: string;
    municipi: string;
    municipiLabel: string;
    provincia: string;
    nifSubjectePassiu: string;
}

export interface Tramit {
    idTramit: string;
    exercici: string;
    establiment: Establishment;
    isComptatge: boolean;
    numJustificantComplementari: string | null;
    quotaLiquidaComplementari: number | null;
    dataPresentacioComplementari: Date | null;
}

export interface Draft {
    idTramit: Nullable<string>;
    exercici: Nullable<string>;
    model: Nullable<string>;
    idEstabliment: Nullable<string>;
    isComptatge: Nullable<boolean>;
    numJustificantComplementari: Nullable<string>;
    quotaLiquidaComplementari: Nullable<number>;
    /** ISO Date string "YYYY-MM-DDThh:mm:ss.zzz" */
    dataPresentacioComplementari: Nullable<string>;
}

export interface DadesInicialsResponse extends SeHttpResponse {
    content?: Nullable<Draft>;
}

// Request READ: Province and Municipality
export interface RequestReadProvinceMunicipality {
	postalCode: string;
	ca: string;
	indAsList: boolean;
}

export interface SelfAssessment {
    numJustificant?: string;
    idDocuments?: string[];
    idAutoliquidacio?: string;
    quotaLiquida?: number;
    quotaTributaria?: number;
    subjectePassiu?: { nif: string; nom: string };
    tipus?: string;
    estat?: SelfAssessmentState;
    dataPresentacio?: string;
};

export enum SelfAssessmentStateEnum {
    GENERAT = 'GENERAT',
    ESBORRANY = 'ESBORRANY',
    NO_PRESENTAT = 'NO_PRESENTAT',
    PENDENT_PRESENTACIO = 'PENDENT_PRESENTACIO',
    PRESENTANT = 'PRESENTANT',
    PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
    PRESENTAT = 'PRESENTAT',
    PAGAT = 'PAGAT',
    PAGANT = 'PAGANT',
    PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
    PAGAMENT_ERROR = 'PAGAMENT_ERROR',
    TRAMITAT = 'TRAMITAT',
    TRAMITANT = 'TRAMITANT',
    TRAMITACIO_ERROR = 'TRAMITACIO_ERROR',
    CONSOLIDANT = 'CONSOLIDANT',
    CONSOLIDAT = 'CONSOLIDAT',
    CONSOLIDACIO_ERROR = 'CONSOLIDACIO_ERROR',
    ERROR = 'ERROR',
}

export type SelfAssessmentState = `${SelfAssessmentStateEnum}`;

export interface SearchRequestParameters {
    idTramit: string;
    impost: string;
    exercici: string;
    periodi: string;
    model: string;
    clau: string;
  }

export interface WorkingSessionResponse extends SeHttpResponse {
content?: Nullable<string>;
}