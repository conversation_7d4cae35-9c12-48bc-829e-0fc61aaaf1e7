import {
  Component,
  OnInit,
  type <PERSON><PERSON><PERSON><PERSON>,
  Query<PERSON>ist,
  ElementRef,
  ViewChildren,
  AfterViewInit,
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
  type FormControl,
  ValidationErrors,
} from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  forkJoin,
  map,
  take,
  takeUntil,
} from 'rxjs';

import {
  SeDropdownOption,
  SeModal,
  SeModalService,
  type Nullable,
  SeValidations,
  SeSpinnerService,
} from 'se-ui-components-mf-lib';
import {
  AppRoutes,
  HeaderInfoService,
  SelfAssessmentStatusEnum,
  StoreService,
} from 'src/app/core';
import { Constant } from 'src/app/core/models/constants.enum';
import { YearEstablishmentEndpointService } from './year-establishment-endpoint.service';
import {
  Draft,
  Establishment,
  RequestReadProvinceMunicipality,
  SearchRequestParameters,
  SelfAssessment,
} from './year-establishment.model';

@Component({
  selector: 'app-year-establishment',
  styleUrls: ['year-establishment.component.scss'],
  templateUrl: 'year-establishment.component.html',
})
export class YearEstablishmentComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private destroyed$: Subject<void>;

  /* FORMS */
  protected componentForm: FormGroup;
  protected createEstablishmentForm: FormGroup | undefined;
  protected idTramit: Nullable<string> = null;
  protected beforeIdTramit: Nullable<string> = null;
  protected searchComplementary!: SearchRequestParameters;
  protected exerciciChange: number | undefined;

  /* DROPDOWNS OPTIONS */
  protected establishmentList: SeDropdownOption[] | undefined;
  protected numberingTypeList: SeDropdownOption[] | undefined;
  protected categoriesList: SeDropdownOption[] | undefined;
  protected addressTypeList: SeDropdownOption[] | undefined;
  protected provinceList: SeDropdownOption[] | undefined;
  protected municipyList: SeDropdownOption[] | undefined;

  /* HANDLE ESTABLISHMENT BUTTON */
  protected disableExistingEstablishments: boolean = false;
  protected buttonEstablishmentLabel: string = this.translateService.instant(
    'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.ADD_ESTABLISHMENT',
  );

  /* DATA ESTABLISHMENT */
  private allEstablishmentDataList: Establishment[] | undefined;

  /* Autoliquidación Complementaria */
  protected isAutomaticComplementaryVisible = false;
  protected automaticComplementaryDescription = '';
  protected automaticComplementarySearchKey: Nullable<string>;
  protected continueWithoutSearch = true;
  protected readonly removeComplementaryTableColumns = ['tipus'];

  @ViewChildren('yearPeriod') yearPeriodElements:
    | QueryList<ElementRef>
    | undefined;

  constructor(
    protected store: StoreService,
    private fb: FormBuilder,
    private yearEstablishmentService: YearEstablishmentEndpointService,
    private translateService: TranslateService,
    private router: Router,
    private modalService: SeModalService,
    private header: HeaderInfoService,
    private spinnerService: SeSpinnerService,
  ) {
    this.resetStore();
    this.destroyed$ = new Subject<void>();

    this.componentForm = this.fb.group({
      idTramit: [null, Validators.required],
      exercici: [null, Validators.required],
      establiment: [null, this.establimentValidator.bind(this)],
      isComptatge: [null, Validators.required],
      numJustificantComplementari: [null],
      quotaLiquidaComplementari: [null],
      dataPresentacioComplementari: [null],
    });

    this.fillComplementaryParameters();
  }

  get descriptionLabel(): string {
    return this.createEstablishmentForm
      ? 'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.DESCRIPTION_1'
      : 'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.DESCRIPTION';
  }

  ngOnInit(): void {
    this.idTramit = this.store.idTramit;
  }

  ngAfterViewInit(): void {
    // Detectar cuando el componente de ejercicio ha sido cargardo para poder sobreescribir el año si tenemos draft
    this.yearPeriodElements?.changes?.subscribe(
      (elements: QueryList<ElementRef>) => {
        const customElement = elements.first?.nativeElement;
        if (customElement) {
          this.callEstablishmentAndInitialData();
          this.subscribeToEstablimentValueChanges();
          this.subscribeToIsComptatgeValueChanges();
        }
      },
    );
  }

  private fillComplementaryParameters(): void {
    this.searchComplementary = {
      idTramit: this.store.idTramit!,
      impost: 'IGEC',
      exercici: this.exercici,
      periodi: '0A',
      model: '910',
      clau: '',
    };
  }

  private resetStore(): void {
    this.store.establishmentCategoryId = null;
    this.store.isCountingModeWithInstrument = null;
    this.store.taxYear = null;
    this.store.model = null;
    this.header.resetYearEstablishment();
  }

  private callEstablishmentAndInitialData(): void {
    this.componentForm.get('idTramit')?.setValue(this.idTramit);
    this.spinnerService.enableSpinnerManualStopMode();

    forkJoin([
      this.yearEstablishmentService.getInitialData(this.idTramit!),
      this.yearEstablishmentService.getEstablishment(this.idTramit!),
    ])
      .pipe(
        takeUntil(this.destroyed$),
        map(([dataFromDraft, establishmentList]) => {
          return {
            establishmentList: establishmentList.content,
            dataFromDraft: dataFromDraft.content,
          };
        }),
      )
      .subscribe((value) => {
        this.spinnerService.start();
        if (value.dataFromDraft?.idEstabliment) {
          this.fillDataFromDraft(value.dataFromDraft);
        }
        this.allEstablishmentDataList = value.establishmentList;
        if (
          this.allEstablishmentDataList &&
          this.allEstablishmentDataList.length > 0
        ) {
          this.setExistingEstablishments(this.allEstablishmentDataList);
        } else {
          this.callReferenceDataAndCreateEstablishment();
          this.spinnerService.stopAndDisableManualStopMode();
        }
      });
  }

  private fillDataFromDraft(draft: Draft): void {
    this.componentForm.patchValue({
      idTramit: draft.idTramit,
      exercici: draft.exercici,
      establiment: draft.idEstabliment,
      isComptatge: draft.isComptatge,
      numJustificantComplementari: draft.numJustificantComplementari,
      quotaLiquidaComplementari: draft.quotaLiquidaComplementari,
      dataPresentacioComplementari: draft.dataPresentacioComplementari,
    });

    /* Si los datos de la sesión guardada indican que la autoliquidación que
    se guardó era una autoliquidación complementaria, entonces el número
    de justificante aparecerá en la cabecera y no se mostrará la opción de
    hacer una complementaria de nuevo porque ya es una complementaria. */
    if (!draft.numJustificantComplementari) {
      this.searchComplementary = {
        ...this.searchComplementary,
        exercici: draft.exercici!,
      };
    } else {
      this.isAutomaticComplementaryVisible = false;
    }
    this.exerciciChange = Number(draft.exercici);
    this.header.receiptId = draft.numJustificantComplementari;
    this.header.resetYearEstablishment();
  }

  protected onTaxYearChange(event: Event): void {
    const details = event as CustomEvent;
    const currentYear = details?.detail?.year;
    const yearFormValue: string | null = this.yearControl.value as
      | string
      | null;

    if (currentYear && yearFormValue && currentYear !== +yearFormValue) {
      this.componentForm.get('exercici')?.setValue(details.detail.year);
      this.searchComplementary.exercici = details.detail.year;
      /* Si cambia el ejercicio se tiene que reiniciar
      la vista: contaje y complementaria */
      this.header.receiptId = null;
      this.isComptatgeControl.setValue(null);
    } else {
      this.componentForm.get('exercici')?.setValue(currentYear);
      this.searchComplementary.exercici = currentYear;
    }
  }

  private setExistingEstablishments(
    establishmentsResponse: Establishment[],
  ): void {
    this.establishmentList = establishmentsResponse.map(
      (establishment: Establishment) => {
        return {
          id: establishment.id,
          label: this.getEstablishmentLabel(establishment),
        };
      },
    );
    this.orderEstablishments();
    this.spinnerService.stopAndDisableManualStopMode();
  }

  private orderEstablishments(): void {
    if (this.establishmentList) {
      this.establishmentList.sort((a, b) => {
        const [nombreA, direccionA, categoriaA] = a.label.split(' | ');
        const [nombreB, direccionB, categoriaB] = b.label.split(' | ');

        const concatenatedA = nombreA + direccionA + categoriaA;
        const concatenatedB = nombreB + direccionB + categoriaB;

        return concatenatedA.localeCompare(concatenatedB);
      });
    }
  }

  private getEstablishmentLabel(
    establishment: Establishment,
    addCategory: boolean = true,
  ): string {
    const addressParts = {
      nomComercial: establishment.nomComercial,
      tipusViaLabel: establishment.tipusViaLabel,
      nomVia: establishment.nomVia,
      num: establishment.num,
      codiPostal: establishment.codiPostal,
      municipiLabel: establishment.municipiLabel,
      categoriaLabel: establishment.categoriaLabel,
    };

    const formattedAddress =
      `${addressParts.nomComercial || ''}` +
      `${addressParts.tipusViaLabel ? `| ${addressParts.tipusViaLabel}` : ''} ` +
      `${addressParts.nomVia || ''} ${addressParts.num || ''} ` +
      `(${addressParts.municipiLabel || ''}) ` +
      `${addCategory && addressParts.categoriaLabel ? `| ${addressParts.categoriaLabel}` : ''}`;

    return formattedAddress.replace(/\s*\|\s*$/, '').trim();
  }

  private callReferenceDataAndCreateEstablishment(): void {
    const sources = [
      this.yearEstablishmentService.getCategories(),
      this.yearEstablishmentService.getNumberingType(),
      this.yearEstablishmentService.getAddressType(),
    ];

    forkJoin(sources)
      .pipe(
        takeUntil(this.destroyed$),
        map(([categories, numberingTypes, addressTypes]) => {
          return {
            categories,
            numberingTypes,
            addressTypes,
          };
        }),
      )
      .subscribe((response) => {
        this.categoriesList = response.categories.content;
        this.addressTypeList = response.addressTypes.content;
        this.numberingTypeList = response.numberingTypes.content;
        this.createEmptyEstablishment();
      });
  }

  private createEmptyEstablishment(): void {
    const newEstablismentFields = {
      nomComercial: ['', [Validators.required, Validators.maxLength(200)]],
      categoria: ['', Validators.required],
      tipusVia: ['', Validators.required],
      nomVia: ['', [Validators.required, Validators.maxLength(50)]],
      tipusNumeracio: ['', Validators.required],
      num: ['', [Validators.required, Validators.maxLength(4)]],
      lletra: ['', Validators.maxLength(2)],
      escala: [''],
      pis: ['', Validators.maxLength(2)],
      portal: ['', Validators.maxLength(2)],
      codiPostal: ['', [Validators.required, SeValidations.postalCode]],
      provincia: ['', Validators.required],
      municipi: ['', Validators.required],
      // Only visual field
      municipiLabel: [''],
      categoriaLabel: [''],
      tipusViaLabel: [''],
    };

    this.createEstablishmentForm = this.fb.group(newEstablismentFields);
    this.createEstablishmentForm
      .get('tipusNumeracio')
      ?.valueChanges.pipe(takeUntil(this.destroyed$), debounceTime(500))
      .subscribe((value) => {
        const numControl = this.createEstablishmentForm?.get('num');
        if (value === '4') {
          // S/N
          numControl?.disable();
          numControl?.setValue('');
        } else {
          numControl?.enable();
          if (value && !numControl?.value) {
            numControl?.markAsTouched();
          }
        }
      });

    this.createEstablishmentForm.valueChanges.subscribe(() => {
      this.componentForm.get('establiment')?.updateValueAndValidity();
    });
  }

  protected handleAddEstablishmentButton(): void {
    this.isComptatgeControl.setValue(null);

    if (!this.createEstablishmentForm) {
      if (
        !this.addressTypeList ||
        !this.numberingTypeList ||
        !this.categoriesList
      ) {
        this.callReferenceDataAndCreateEstablishment();
      } else {
        this.createEmptyEstablishment();
      }
      this.componentForm.get('establiment')?.disable();
      this.establishmentControl.setValue('');
      this.buttonEstablishmentLabel = this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.SELECT_ESTABLISHMENT',
      );
    } else {
      this.createEstablishmentForm = undefined;
      this.componentForm.get('establiment')?.enable();
      this.establishmentControl.setValidators(Validators.required);
      this.buttonEstablishmentLabel = this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.ESTABLIMENT.ADD_ESTABLISHMENT',
      );
    }
  }

  protected disabledField(list: SeDropdownOption[] | undefined): boolean {
    return !!(list && list.length === 1);
  }

  protected onBlurPostalCode(postalCode: number): void {
    if (this.validatePostalCode(postalCode)) {
      const request: RequestReadProvinceMunicipality = {
        ca: this.translateService.currentLang,
        postalCode: postalCode.toString(),
        indAsList: true,
      };
      this.yearEstablishmentService
        .getProviceAndMunicipalityList(request)
        .subscribe((response) => {
          this.provinceList = response.content?.province;
          this.municipyList = response.content?.locality;
          if (
            response.content?.province.length > 0 &&
            response.content?.locality.length > 0
          ) {
            this.createEstablishmentForm?.patchValue({
              provincia: response.content?.province[0].id,
              municipi: response.content?.locality[0].id,
            });
            if (this.disabledField(this.provinceList)) {
              this.createEstablishmentForm?.get('provincia')?.disable();
            }
            if (this.disabledField(this.municipyList)) {
              this.createEstablishmentForm?.get('municipi')?.disable();
            }
          }
        });
    }
  }

  private validatePostalCode(cp: number): boolean {
    const format = /^[0-9]{5}$/;
    return format.test(cp.toString());
  }

  private searchEstablishmentById(id: string): Establishment | undefined {
    return this.allEstablishmentDataList?.find(
      (establishment) => establishment.id === id,
    );
  }

  protected isFormIncomplete(): boolean {
    return this.createEstablishmentForm
      ? this.createEstablishmentForm.invalid || this.componentForm.invalid
      : this.componentForm.invalid;
  }

  private establimentValidator(
    control: AbstractControl,
  ): ValidationErrors | null {
    if (!this.createEstablishmentForm) {
      return Validators.required(control);
    }
    return null;
  }

  protected goBack(): void {
    this.router.navigate([AppRoutes.PARTICIPANTS]);
  }

  protected submit(): void {
    this.saveData();
  }

  protected saveData(): void {
    const formData = this.componentForm.getRawValue();
    let establishmentData: Establishment | undefined;
    // If the user has created a new establishment
    if (this.createEstablishmentForm) {
      establishmentData = this.createEstablishmentForm.getRawValue();
      if (establishmentData) {
        establishmentData.tipusViaLabel =
          this.addressTypeList?.find(
            (type) => type.id === establishmentData!.tipusVia,
          )?.label ?? '';
        establishmentData.categoriaLabel =
          this.categoriesList?.find(
            (category) => category.id === establishmentData!.categoria,
          )?.label ?? '';
        establishmentData.municipiLabel =
          this.municipyList?.find(
            (municipy) => municipy.id === establishmentData!.municipi,
          )?.label ?? '';
      }
    }
    // has selected an existing one
    else {
      establishmentData = this.searchEstablishmentById(formData?.establiment);
    }
    formData.establiment = establishmentData;
    this.yearEstablishmentService
      .postInitialData(formData)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        if (response.content) {
          this.store.establishmentCategoryId = formData.establiment.categoria;
          this.store.isCountingModeWithInstrument = formData.isComptatge;
          this.store.taxYear = formData.exercici;
          this.store.model = Constant.IGEC_MODEL;
          this.header.receiptId = formData.numJustificantComplementari || null;
          this.header.status = SelfAssessmentStatusEnum.IN_PROGRESS;
          this.header.taxYear = formData.exercici;
          this.header.model = Constant.IGEC_MODEL;
          this.header.establishment = this.getEstablishmentLabel(
            formData.establiment,
            false,
          ).replaceAll('|', ',');
          this.store.establishment = this.getEstablishmentLabel(
            formData.establiment,
          ).replaceAll('|', ',');
          this.router.navigate([AppRoutes.TAX_DECLARATION]);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private verifyIfHaveWorkingSession(clau: string): void {
    this.yearEstablishmentService
      .getWorkingSession(this.idTramit, this.exercici, clau)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        if (response?.content && this.store.hasAppearedWorkingSession) {
          this.beforeIdTramit = response.content; // save id previous working session
          this.openWorkingSessionWarningModal();
        } else {
          // buscar complementaria cuando no hay sesion de trabajo y no es ya una complementaria
          const alreadyComplementary = this.componentForm.get(
            'numJustificantComplementari',
          )?.value;
          if (!alreadyComplementary) {
            this.searchComplementaryData(this.idTramit!);
          }
        }
      });
  }

  private openWorkingSessionWarningModal(): void {
    const data: SeModal = {
      title: this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.WORKING_SESSION.MODAL.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.WORKING_SESSION.MODAL.DETAIL',
      ),
      closable: true,
      closableLabel: this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.WORKING_SESSION.MODAL.CANCEL_BUTTON',
      ),
      secondaryButton: true,
      secondaryButtonLabel: this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.WORKING_SESSION.MODAL.ACCEPT_BUTTON',
      ),
    };

    const modalRef = this.modalService.openModal(data);

    modalRef.componentInstance.modalOutputEvent
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        this.searchComplementaryData(this.idTramit!);
        modalRef.close();
      });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        // Restore previous working session
        this.idTramit = this.beforeIdTramit;
        this.store.idTramit = this.beforeIdTramit;
        this.componentForm.get('idTramit')?.setValue(this.beforeIdTramit);
        this.searchPreviousTramitData();
        modalRef.close();
      });
  }

  private searchPreviousTramitData(): void {
    this.yearEstablishmentService
      .getInitialData(this.beforeIdTramit!)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response) => {
        // Si no es una complementaria, buscamos si el trámite tiene complementaria
        if (response?.content) {
          this.componentForm
            .get('isComptatge')
            ?.setValue(response.content.isComptatge);
          if (!response.content.numJustificantComplementari) {
            this.searchComplementaryData(this.beforeIdTramit!);
          } else {
            // Adjuntamos datos para continuar con la complementaria
            this.componentForm.patchValue({
              numJustificantComplementari:
                response.content.numJustificantComplementari,
              quotaLiquidaComplementari:
                response.content.quotaLiquidaComplementari,
              dataPresentacioComplementari:
                response.content.dataPresentacioComplementari,
            });
          }
        }
      });
  }

  private subscribeToIsComptatgeValueChanges(): void {
    this.isComptatgeControl.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe((isComptatge: Nullable<boolean>) => {
        this.updateComplementaryComponentsVisibility(
          isComptatge,
          this.establishmentControl.value,
        );
      });
  }

  private subscribeToEstablimentValueChanges(): void {
    this.establishmentControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.destroyed$))
      .subscribe((selectedStablishmentId: Nullable<string>) => {
        /* Si cambia el establecimiento se tiene que reiniciar
        la vista: complementaria y contaje */
        this.header.receiptId = null;
        this.isComptatgeControl.setValue(null);
        this.updateComplementaryComponentsVisibility(
          this.isComptatgeControl.value,
          selectedStablishmentId,
        );
      });
  }

  private get establishmentControl(): FormControl<Nullable<string>> {
    return this.componentForm.get('establiment') as FormControl<
      Nullable<string>
    >;
  }

  private get isComptatgeControl(): FormControl<Nullable<boolean>> {
    return this.componentForm.get('isComptatge') as FormControl<
      Nullable<boolean>
    >;
  }

  protected get yearControl(): FormControl<Nullable<boolean>> {
    return this.componentForm.get('exercici') as FormControl<Nullable<boolean>>;
  }

  private updateComplementaryComponentsVisibility(
    isComptatge: Nullable<boolean>,
    selectedStablishmentId: Nullable<string>,
  ): void {
    const isComptatgeSelected = typeof isComptatge === 'boolean';
    if (!isComptatgeSelected || this.createEstablishmentForm) {
      this.hideComplementary();
      return;
    }

    this.automaticComplementarySearchKey = this.createComplementarySearchKey(
      selectedStablishmentId,
    );

    if (selectedStablishmentId && this.automaticComplementarySearchKey) {
      this.verifyIfHaveWorkingSession(this.automaticComplementarySearchKey);
    } else {
      this.searchComplementaryData(this.idTramit!);
    }
  }

  private searchComplementaryData(idTramit: string): void {
    this.searchComplementary = {
      ...this.searchComplementary,
      idTramit,
      clau: this.automaticComplementarySearchKey ?? '',
    };

    this.isAutomaticComplementaryVisible =
      !!this.automaticComplementarySearchKey;
  }

  private createComplementarySearchKey(
    selectedStablishmentId: Nullable<string>,
  ): Nullable<string> {
    const selectedTaxpayer = this.store.selectedTaxpayer;

    if (!selectedTaxpayer?.nif || !this.exercici) {
      return null;
    }
    return `${this.exercici}_${selectedTaxpayer.nif}_${selectedStablishmentId}`;
  }

  protected get exercici(): string {
    return this.componentForm.get('exercici')?.value ?? '';
  }

  protected submitComplementary(event: Event): void {
    const { detail } = event as CustomEvent<Nullable<SelfAssessment>>;

    if (!detail) return;

    const { dataPresentacio, numJustificant, quotaTributaria } = detail;

    this.componentForm.patchValue({
      numJustificantComplementari: numJustificant,
      quotaLiquidaComplementari: quotaTributaria,
      dataPresentacioComplementari: dataPresentacio,
    });

    this.submit();
  }

  protected onPayButtonClick(event: Event): void {
    const { detail } = event as CustomEvent<SelfAssessment>;
    this.store.selfassessmentId = detail.idAutoliquidacio;
    this.store.amountToPay = detail.quotaLiquida;
    this.router.navigate([AppRoutes.PAGAMENT]);
  }

  protected onAutomaticComplementarySearchEnd(event: Event): void {
    const { detail } = event as CustomEvent<{
      selfassessments: SelfAssessment[];
      hiddenSelfassessmentsExist: boolean;
    }>;
    const { selfassessments, hiddenSelfassessmentsExist } = detail;

    // Si es true desactivamos el botón de continuar. Solo puede continuar complementando manualmente.
    this.continueWithoutSearch =
      !hiddenSelfassessmentsExist && selfassessments.length === 0;

    /* SÍ que se han encontrado autoliquidaciones presentadas para el ejercicio
      y establecimiento seleccionados con la búsqueda automática. No se debe
      mostrar el componente de la complementaria manual. */
    this.automaticComplementaryDescription = this.translateService.instant(
      hiddenSelfassessmentsExist
        ? 'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.COMPLEMENTARY.DESCRIPTION_DIFFERENT_PRESENTER'
        : 'SE_GRANS_ESTABLIMENTS_MF.MODULE_YEAR_ESTABLISHMENT.COMPLEMENTARY.DESCRIPTION_SAME_PRESENTER',
      { year: this.exercici },
    );
  }

  protected get isNextButtonVisible(): boolean {
    return !this.isAutomaticComplementaryVisible || this.continueWithoutSearch;
  }

  protected onContinueWithoutSearch(event: Event): void {
    this.continueWithoutSearch = !(event as CustomEvent).detail;
  }

  private hideComplementary(): void {
    this.isAutomaticComplementaryVisible = false;
    this.automaticComplementaryDescription = '';
    this.automaticComplementarySearchKey = '';
    this.searchComplementary.clau = '';
    this.header.receiptId = null;

    this.componentForm.patchValue({
      numJustificantComplementari: null,
      quotaLiquidaComplementari: null,
      dataPresentacioComplementari: null,
    });
  }
}
