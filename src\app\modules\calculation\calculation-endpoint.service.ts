import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { RequestSaveSelfAssessment } from './calculation.model';

@Injectable({
  providedIn: 'root',
})
export class CalculationEndpointService {
  constructor(private httpService: SeHttpService) {
    //Intencionadamente vacío
  }

   // GET - Search self-assessment calculation data in BD associated with the corresponding tax
   getCalcul(idTramit: string): Observable<SeHttpResponse> {
    return this.httpService.get({
    method: 'get',
    baseUrl: environment.baseUrlGransEstabliments,
    url: `/${idTramit}/calcul`,
    clearExceptions: true
  })
}

   // POST - Save self-assessment calculation data 
   saveSelfAssessment(request: RequestSaveSelfAssessment): Observable<SeHttpResponse> {
    return this.httpService.post({
    method: 'post',
    baseUrl: environment.baseUrlGransEstabliments,
    url: `/autoliquidacio`,
    body: request,
    clearExceptions: true
  })
}
}
