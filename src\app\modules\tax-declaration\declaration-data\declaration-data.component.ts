import {
  Component,
  OnInit,
  OnDestroy,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  takeUntil,
  throttleTime,
} from 'rxjs';
import {
  Row,
  SeDropdownOption,
  SeModal,
  SeModalService,
  SeValidations,
} from 'se-ui-components-mf-lib';

import { ModalContentCorrectionCoefficientComponent } from './modal-content-correction-coefficient/modal-content-correction-coefficient.component';
import {
  CountDeclarationData,
  NonCountDeclarationData,
} from '../models/tax-declaration.model';

@Component({
  selector: 'app-declaration-data',
  styleUrls: ['declaration-data.component.scss'],
  templateUrl: './declaration-data.component.html',
})
export class DeclarationDataComponent implements OnInit, OnD<PERSON>roy {
  @Input() isCountingModeWithInstrument = false;
  @Input() typologyPlacesForCoefficientCorrectorModal: string[] = [];
  @Input() tableBodyCoefficientCorrector: Row[] = [];
  @Input() establishmentSituationList: SeDropdownOption[] = [];
  @Input() set exemptMinimum(minimumCurrentDeclared: number) {
    if (minimumCurrentDeclared) {
      this.initMinimumVehicleExemption(minimumCurrentDeclared);
    }
  }
  @Input() set ratio(ratio: number) {
    if (ratio) {
      this.formEstablishment.get('ratio')?.patchValue(ratio);
    }
  }
  @Input() set coefficient(coefficient: number) {
    if (coefficient) {
      this.formCorrectorCoefficient
        .get('correctionCoefficient')
        ?.patchValue(coefficient);
    }
  }
  @Input() set nonCountDeclarationData(
    nonCountDeclarationData: NonCountDeclarationData | undefined
  ) {
    if (
      nonCountDeclarationData &&
      nonCountDeclarationData?.openingDays >= 0 &&
      nonCountDeclarationData?.openingDays !== null
    ) {
      this.formEstablishment.patchValue(nonCountDeclarationData);
    }
  }
  @Input() set countDeclarationData(
    countDeclarationData: CountDeclarationData | undefined
  ) {
    if (
      countDeclarationData &&
      countDeclarationData?.numberVehicles >= 0 &&
      countDeclarationData?.numberVehicles !== null
    ) {
      this.formCorrectorCoefficient.patchValue(countDeclarationData);
    }
  }
  @Input() set isDisableTaxDeclarationForm(isDisable: boolean) {
    if (isDisable) {
      this.formCorrectorCoefficient?.disable();
    } else {
      this.formCorrectorCoefficient?.get('numberVehicles')?.enable();
      this.formCorrectorCoefficient?.get('vehiclesExcluded')?.enable();
    }
  }

  @Output() handleChangeMinimumVehicleExemption: EventEmitter<number> =
    new EventEmitter();
  @Output()
  handleChangeTaxableBaseNonCountDeclarationData: EventEmitter<NonCountDeclarationData> =
    new EventEmitter();
  @Output()
  handleChangeTaxableBaseCountDeclarationData: EventEmitter<CountDeclarationData> =
    new EventEmitter();
  @Output() handleDisableSubmitButton: EventEmitter<boolean> =
    new EventEmitter();

  protected formCorrectorCoefficient!: FormGroup;
  protected formEstablishment!: FormGroup;
  protected formVehiclesExemption!: FormGroup;
  protected readonly exemptMinimumValue: number = 27000;
  private destroyed$: Subject<void> = new Subject<void>();

  get declarationTitle(): string {
    const title = `SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.${
      this.isCountingModeWithInstrument
        ? 'DATA_DECLARATION'
        : 'DATA_FOR_DECLARATION'
    }.TITLE`;

    return this.translateService.instant(title);
  }

  get declarationIndication(): string {
    const indication = `SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.${
      this.isCountingModeWithInstrument
        ? 'DATA_DECLARATION.ENTER_NUMBER_OF_VEHICLES'
        : 'DATA_FOR_DECLARATION.ENTER_THE_SURFACE_AREA_IN_SQUARE_METRES'
    }`;

    return this.translateService.instant(indication);
  }

  get ratioValue(): number {
    return this.formEstablishment.get('ratio')?.value;
  }

  get isEstablishmentFormValid(): boolean {
    return (
      this.formEstablishment.valid &&
      this.formVehiclesExemption.get('minimumVehicles')?.value <=
        this.exemptMinimumValue
    );
  }
  get isCorrectorCoefficientFormValid(): boolean {
    return (
      this.formCorrectorCoefficient.valid &&
      this.formVehiclesExemption.get('minimumVehicles')?.value <=
        this.exemptMinimumValue
    );
  }

  constructor(
    private fb: FormBuilder,
    private modalService: SeModalService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    if (this.isCountingModeWithInstrument) {
      this.initCorrectorCoefficientForm();
    } else {
      this.initEstablishmentForm();
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected handleOpenCorrectionCoefficientInfo(): void {
    const data: SeModal = {
      closable: true,
      titleTextWeight: 'bold',
      title: this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.WHAT_IS_CORRECTION_COEFFICIENT'
      ),
      subtitle: this.translateService.instant(
        'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.CORRECTION_COEFFICIENT_APPLIED_TO_VEHICLES'
      ),
      component: ModalContentCorrectionCoefficientComponent,
    };

    const component: ModalContentCorrectionCoefficientComponent =
      this.modalService.openSidebarModal(data, 'right').componentInstance;
    component.typologyPlacesForCoefficientCorrectorModal =
      this.typologyPlacesForCoefficientCorrectorModal;
    component.tableBodyCoefficientCorrector =
      this.tableBodyCoefficientCorrector;
  }

  protected onEnableMinimumVehicleExemption(): void {
    this.formVehiclesExemption.enable();
  }

  private initCorrectorCoefficientForm(): void {
    this.formCorrectorCoefficient = this.fb.group({
      numberVehicles: [
        null,
        [Validators.required, Validators.min(1), Validators.max(*********)],
      ],
      vehiclesExcluded: [null, Validators.required],
      correctionCoefficient: null,
      taxableBase: null,
    });

    this.formCorrectorCoefficient.disable();

    this.formCorrectorCoefficient.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        throttleTime(500),
        takeUntil(this.destroyed$)
      )
      .subscribe(({ numberVehicles, vehiclesExcluded }) => {
        if (vehiclesExcluded >= 0 && numberVehicles > 0) {
          const baseValue = this.calculateBaseWithCount(
            vehiclesExcluded,
            numberVehicles
          );
          this.formCorrectorCoefficient
            .get('taxableBase')
            ?.patchValue(baseValue, { emitEvent: false });

          this.handleDisableSubmitButton.emit(
            !this.isCorrectorCoefficientFormValid
          );
          this.handleChangeTaxableBaseCountDeclarationData.emit({
            ...this.formCorrectorCoefficient.value,
            base: baseValue,
          });
        } else {
          this.formCorrectorCoefficient.get('taxableBase')?.patchValue(null);
          this.handleDisableSubmitButton.emit(true);
        }
      });
  }

  private calculateBaseWithCount(
    vehiclesExcluded: number,
    numberVehicles: number
  ): number {
    const coefficient = this.formCorrectorCoefficient.get(
      'correctionCoefficient'
    )?.value;

    if (vehiclesExcluded > numberVehicles) {
      return 0;
    } else {
      const effectiveVehicles = +numberVehicles - (+vehiclesExcluded || 0);
      return Math.round(effectiveVehicles * coefficient);
    }
  }

  private initEstablishmentForm(): void {
    this.formEstablishment = this.fb.group({
      ratio: { value: null, disabled: true },
      surface: [
        null,
        [
          Validators.required,
          Validators.min(0),
          Validators.max(999999.99),
          Validators.maxLength(9),
        ],
      ],
      openingDays: [
        null,
        [Validators.required, Validators.min(1), Validators.max(366)],
      ],
      situation: [null, [Validators.required]],
      base: { value: null, disabled: true },
    });

    this.formEstablishment.valueChanges
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        throttleTime(500),
        takeUntil(this.destroyed$)
      )
      .subscribe(({ openingDays, situation, surface }) => {
        if (openingDays && situation && surface) {
          const base = +(
            this.ratioValue *
            surface *
            openingDays *
            situation
          ).toFixed(0);
          this.formEstablishment
            .get('base')
            ?.patchValue(base, { emitEvent: false });

          this.handleChangeTaxableBaseNonCountDeclarationData.emit({
            ...this.formEstablishment.value,
            base,
          });

          this.handleDisableSubmitButton.emit(!this.isEstablishmentFormValid);
        } else {
          this.handleDisableSubmitButton.emit(true);
        }
      });
  }

  private initMinimumVehicleExemption(minimumCurrentDeclared: number): void {    
    this.formVehiclesExemption = this.fb.group({
      minimumVehicles: [
        minimumCurrentDeclared,
        SeValidations.listValidations([
          { validator: Validators.min(0) },
          { validator: Validators.required },
          {
            validator: Validators.max(this.exemptMinimumValue),
            translation:
              'SE_GRANS_ESTABLIMENTS_MF.MODULE_TAX_DECLARATION.DATA_DECLARATION.MINIMUM_EXEMPTION_VEHICLES_ERROR',
            translateParams: { minimum: this.exemptMinimumValue },
          },
        ]),
      ],
    });

    this.formVehiclesExemption.disable();

    this.formVehiclesExemption
      .get('minimumVehicles')
      ?.valueChanges.pipe(debounceTime(500), takeUntil(this.destroyed$))
      .subscribe((minimumVehicles) => {
        this.handleChangeMinimumVehicleExemption.emit(minimumVehicles);

        if (this.isCountingModeWithInstrument) {
          this.handleDisableSubmitButton.emit(
            !this.isCorrectorCoefficientFormValid
          );
        } else {
          this.handleDisableSubmitButton.emit(!this.isEstablishmentFormValid);
        }
      });
  }
}
