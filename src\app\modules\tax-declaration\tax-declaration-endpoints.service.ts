import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpService } from 'se-ui-components-mf-lib';

import { environment } from 'src/environments/environment';
import { GetTypologyHttpResponse } from './models/typology.model';
import { GetCoefficientHttpResponse } from './models/coefficient-corrector.model';
import { GetRatioHttpResponse } from './models/ratio.model';
import { GetExemptMinimumHttpResponse } from './models/exempt-minimum.model';
import {
  GetTaxDeclarationResponse,
  PostTaxDeclarationResponse,
  TaxDeclaration,
} from './models/tax-declaration.model';

@Injectable({
  providedIn: 'root',
})
export class TaxDeclarationService {
  constructor(private httpService: SeHttpService) {}

  getExemptMinimum(idTramit: string): Observable<GetExemptMinimumHttpResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/exempts`,
      method: 'get',
    });
  }

  // *solo con sistema de conteo ↓
  getTypology(idTramit: string): Observable<GetTypologyHttpResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/tipologias-barem`,
      method: 'get',
    });
  }

  getCoefficients(idTramit: string): Observable<GetCoefficientHttpResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/coeficient-corrector`,
      method: 'get',
    });
  }
  // *solo con sistema de conteo ↑

  // *solo sin sistema de conteo ↓
  getRatio(idTramit: string): Observable<GetRatioHttpResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/ratio`,
      method: 'get',
    });
  }

  getEstablishmentSituations(
    idTramit: string
  ): Observable<GetCoefficientHttpResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/situacio-establiment`,
      method: 'get',
    });
  }
  // *solo sin sistema de conteo ↑

  saveTaxDeclaration(
    body: TaxDeclaration
  ): Observable<PostTaxDeclarationResponse> {
    return this.httpService.post({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/declaracio`,
      method: 'post',
      body,
    });
  }

  getTaxDeclaration(idTramit: string): Observable<GetTaxDeclarationResponse> {
    return this.httpService.get({
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/${idTramit}/declaracio`,
      method: 'get',
    });
  }
}
