import { SeHttpResponse } from "se-ui-components-mf-lib";

export enum EstablishmentCode {
  SmallSupermarket = 'SP',
  LargeSupermarket = 'SG',
  SmallHypermarket = 'HP',
  MediumHypermarket = 'HM',
  LargeHypermarket = 'HG',
  SpecialSurfaces = 'SE',
  CommercialGallery = 'GC'
}

export interface TypologyResponse {
  codigo: EstablishmentCode;
  valor: number;
  descripcio: string;
  mostrar: boolean;
}

export interface GetTypologyHttpResponse extends SeHttpResponse {
  content?: TypologyResponse[];
}
