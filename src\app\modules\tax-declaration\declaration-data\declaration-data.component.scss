@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/variables-dark';
@import 'bootstrap/scss/mixins/breakpoints';

.declaration-data {
  &__vehicles {
    form {
      .se-input,
      p {
        margin-inline: 8px;
      }

      p {
        padding-bottom: 8px;
      }
    }

    @include media-breakpoint-down(md) {
      form {
        p {
          display: none;
        }

        se-input {
          margin-inline: 0;
        }
      }
    }
  }

  &__establishment {
    &--editable-section {
      se-input {
        margin-right: 16px;
      }
    }

    ::ng-deep .input-label {
      justify-content: flex-start !important;
    }
  }

  &__exempt-minimum {
    margin-top: 30px;

    form {
      ::ng-deep {
        .se-input {
          margin-bottom: 0;
        }

        .input-label {
          justify-content: flex-start !important;
        }
      }

      se-button {
        margin-top: 1.35rem;
      }
    }
  }
}
