import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { StoreService } from '../services';
import { AppRoutes } from '../models';

@Injectable({ providedIn: 'root' })
export class CalculationGuard {
  constructor(private router: Router, private store: StoreService) {}

  canActivate() {
    if (this.store.idTramit && this.store.taxYear && this.store.declared === true) {
      return true;
    } else {
      this.router.navigate([AppRoutes.TAX_DECLARATION]);
      return false;
    }
  }
}
