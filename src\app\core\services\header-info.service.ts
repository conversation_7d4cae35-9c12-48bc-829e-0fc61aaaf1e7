import { formatDate } from '@angular/common';
import { Inject, Injectable, LOCALE_ID, type On<PERSON><PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Subject, combineLatest, takeUntil } from 'rxjs';

import {
  SeTagThemeEnum,
  type Nullable,
  type SeHeaderInfoItem,
  type TagProps,
} from 'se-ui-components-mf-lib';
import {
  SelfAssessmentStatusEnum,
  type SelfAssessmentStatus,
  HeaderTagData,
} from '../models';
import { StoreService } from './store.service';

@Injectable({
  providedIn: 'root',
})
export class HeaderInfoService implements OnDestroy {
  private readonly INFO_ITEMS_INITIAL_VALUE = [
    null, // Nombre del sujeto pasivo
    null, // modelo
    null, // siempre null (vacío)
    null, // tipo de trámite
    null, // establecimiento
    null, // núm. justificante complementaria
  ];
  private readonly infoItemsSubject = new BehaviorSubject<
    Nullable<SeHeaderInfoItem>[]
  >(this.INFO_ITEMS_INITIAL_VALUE);
  public readonly infoItems$ = this.infoItemsSubject.asObservable();

  private tagsSubject = new BehaviorSubject<Nullable<TagProps[]>>([]);
  public readonly tags$ = this.tagsSubject.asObservable();

  private presentationDateSubject = new BehaviorSubject<Nullable<string>>(null);
  private presentationDate$ = this.presentationDateSubject.asObservable();

  private translations$ = this.translate.get(
    'SE_GRANS_ESTABLIMENTS_MF.HEADER_INFO',
  );

  private _receiptId: Nullable<string>;

  private destroyed$ = new Subject<void>();

  constructor(
    private translate: TranslateService,
    private store: StoreService,
    @Inject(LOCALE_ID) private locale: string,
  ) {
    this.getStoredData();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.unsubscribe();
  }

  private getStoredData(): void {
    if (this.store.header) {
      this.infoItemsSubject.next(this.store.header);
      this.presentationDateSubject.next(this.store.presentationDate);
      this.tagsSubject.next(this.store.status);
    }
  }

  public resetYearEstablishment(): void {
    this.resetInfoItems(1, 6, null);
  }

  public resetCompleteSubheader(): void {
    this.resetInfoItems(0, 6, null);
  }

  private resetInfoItems(
    start: number,
    end: number,
    fill: SeHeaderInfoItem | null,
  ): void {
    const newItems = this.infoItemsSubject.getValue();
    if (newItems) {
      newItems.fill(fill, start, end);
      this.infoItemsSubject.next([...newItems]);
      this.tagsSubject.next([]);
      this.presentationDateSubject.next(null);
    }
    this.store.presentedAl = false;
  }

  public set taxpayerName(name: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(0, 1, {
          term: translations['TAXPAYER'],
          details: name,
        });
      });
  }

  public set taxYear(year: Nullable<string | number>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const modalityInfoItem = year
          ? {
              term: translations['MODALITY'],
              details: `${translations['PERIOD']} ${String(year)}`,
            }
          : null;
        this.spliceItems(3, 1, modalityInfoItem);
      });
  }

  public set model(model: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const modelInfoItem = model
          ? { term: translations['MODEL'], details: model }
          : null;
        this.spliceItems(1, 1, modelInfoItem);
      });
  }

  public set establishment(establishment: Nullable<string>) {
    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        const establishmentInfoItem = establishment
          ? { term: translations['ESTABLISHMENT'], details: establishment }
          : null;
        this.spliceItems(4, 1, establishmentInfoItem);
      });
  }

  public set status(status: Nullable<SelfAssessmentStatus>) {
    combineLatest([this.translations$, this.presentationDate$])
      .pipe(takeUntil(this.destroyed$))
      .subscribe(([translations, presentationDate]) => {
        if (!status) {
          this.tagsSubject.next([]);
          this.store.status = [];
          return;
        }
        const newTags: Nullable<TagProps[]> = this.createTags({
          translations,
          presentationDate,
          status,
        });
        this.tagsSubject.next(newTags);
        this.store.status = newTags;
      });
  }

  private createTags({
    translations,
    presentationDate,
    status,
  }: HeaderTagData): TagProps[] {
    switch (status) {
      case SelfAssessmentStatusEnum.IN_PROGRESS: {
        return [
          {
            label: translations.STATE.IN_PROGRESS,
            tagTheme: SeTagThemeEnum.WARNING,
          },
        ];
      }

      case SelfAssessmentStatusEnum.PAGAT: {
        return [
          {
            label: translations.STATE.PAGAT,
            tagTheme: SeTagThemeEnum.SUCCESS,
          },
        ];
      }

      case SelfAssessmentStatusEnum.PRESENTAT: // ↓ sigue otro caso ↓
      case SelfAssessmentStatusEnum.PENDENT_PAGAMENT: {
        let label = translations.STATE.PRESENTAT;
        if (presentationDate) {
          const date = formatDate(presentationDate, 'dd/MM/yyyy', this.locale);
          label += `: ${date}`;
        }
        const tags = [{ label, tagTheme: SeTagThemeEnum.SUCCESS }];
        if (status === 'PENDENT_PAGAMENT') {
          tags.push({
            label: translations.STATE.PENDENT_PAGAMENT,
            tagTheme: SeTagThemeEnum.WARNING,
          });
        }
        return tags;
      }

      default: {
        return [];
      }
    }
  }

  public set receiptId(id: Nullable<string>) {
    this._receiptId = id;

    if (!id) {
      this.spliceItems(5, 1, null);
      return;
    }

    this.translations$
      .pipe(takeUntil(this.destroyed$))
      .subscribe((translations) => {
        this.spliceItems(5, 1, {
          term: translations['COMPLEMENTARY_OF'],
          details: id,
        });
      });
  }

  public get receiptId(): Nullable<string> {
    return this._receiptId;
  }

  private spliceItems(
    start: number,
    deleteCount: number,
    ...items: Nullable<SeHeaderInfoItem>[]
  ): void {
    const newItems = this.infoItemsSubject.getValue();
    if (newItems) {
      newItems.splice(start, deleteCount, ...items);
      this.infoItemsSubject.next([...newItems]);
      this.store.header = [...newItems];
    }
  }

  get presentationDate(): Nullable<string> {
    return this.presentationDateSubject.getValue();
  }
  set presentationDate(date: Nullable<string>) {
    this.presentationDateSubject.next(date);
    this.store.presentationDate = date;
  }
}
