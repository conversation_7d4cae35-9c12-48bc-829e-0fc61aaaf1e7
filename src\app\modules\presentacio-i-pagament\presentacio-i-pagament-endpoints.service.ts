import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';

import { SeHttpRequest, SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { StatusAutoliquidacioResponse } from './models/status-autoliquidacio.model';

@Injectable({
  providedIn: 'root'
})
export class PresentacioIPagamentsEndpointsService {

  constructor(
    private httpService: SeHttpService
  ) { }
  
  postPresentation = (idAutoliquidacio: string): Observable<SeHttpResponse> => {

    // TODO CAMBIAR ENDPOINT CON LO QUE SE DECIDA
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlGransEstabliments,
      url: `/autoliquidacio/presentacio`,
      body: { idAutoliquidacio },
      spinner: false,
      method: 'post',
    };

    return this.httpService.post(httpRequest);
  }

  getStatusAutoliquidacio = (idAutoliquidacio: string): Observable<StatusAutoliquidacioResponse> => {

    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlTributs,
      url: `/autoliquidacio/${idAutoliquidacio}`,
      spinner: false,
      method: 'get',
    };

    return this.httpService.get(httpRequest);
  }

  createPaymentNoteFor = (
    idsAutoliquidacions: string[]
  ): Observable<string> => {
    return this.httpService
      .post({
        method: 'post',
        baseUrl: environment.baseUrlTributs,
        url: '/nota-pagament/', // obligatoria la barra "/" del final
        body: { idAutoliquidacio: idsAutoliquidacions },
      })
      .pipe(map((response: SeHttpResponse) => response.content));
  };
}