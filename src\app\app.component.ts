import { Component, isDevMode, type On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Observable, Subject, filter, takeUntil } from 'rxjs';

import {
  LoginCertificates,
  SeAuthService,
  SeLoginService,
  SePageLayoutService,
  SeStep,
  SeUser,
} from 'se-ui-components-mf-lib';
import { HeaderInfoService, StepperService } from './core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnDestroy {
  title = 'se-grans-establiments-mf';
  presenterName!: string;
  presenterNif!: string;
  steps$!: Observable<SeStep[]>;

  private destroyed$ = new Subject<void>();

  constructor(
    public router: Router,
    public pageLayoutService: SePageLayoutService,
    private loginService: SeLoginService,
    private authService: SeAuthService,
    public aRoute: ActivatedRoute,
    private stepperService: StepperService,
    protected header: HeaderInfoService,
  ) {
    console.log('Webcomponent: Grans Establiments > constructor');

    this.router.initialNavigation();
    // Listen to the route changes (updates page layout)
    this.pageLayoutService.listenNavigation();
    // Listen steps changes
    this.stepperService.initializeAvailableRoutes();
    this.steps$ = this.pageLayoutService.getCurrentSteps();

    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroyed$),
      )
      .subscribe(() => {
        // Scroll to top
        window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
      });

    // Secured
    if (window.location.href.includes('/secured')) {
      this.getUser().then((user) => {
        this.setUserData(user);
      });
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private getUser(): Promise<SeUser> {
    return isDevMode() ? this.getUserDevMode() : this.getUserFromSession();
  }

  private async getUserDevMode(): Promise<SeUser> {
    const { content } = await this.loginService.login(true);
    return content?.usuario;
  }

  private getUserFromSession(): Promise<SeUser> {
    return Promise.resolve(this.authService.getSessionStorageUser());
  }

  private setUserData(user: SeUser): void {
    if (user) {
      const isLegalUser =
        user?.certificateType === LoginCertificates.JUR_PERSON ||
        user.certificateType === LoginCertificates.REPRE_JUR;

      this.presenterName = (
        isLegalUser ? user?.companyName : user?.nombreCompleto
      ) as string;
      this.presenterNif = (isLegalUser ? user?.companyId : user?.nif) as string;
    }
  }

  protected onHelpButtonClick(): void {
    window.open('https://atc.gencat.cat/ca/tributs/igec/', '_blank');
  }
}
